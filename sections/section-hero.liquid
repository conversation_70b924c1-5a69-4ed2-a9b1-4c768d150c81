<!-- /sections/section-hero.liquid -->
{%- render 'hero' -%}

{% schema %}
{
  "name": "Image banner",
  "class": "section-overlay-header",
  "settings": [
    {
      "type": "header",
      "content": "Content"
    },
    {
      "type": "image_picker",
      "id": "image_1",
      "label": "Primary image",
      "info": "3200 x 1200px .jpg recommended. [Learn more](https://invisiblethemes.com/link/broadcast/images)"
    },
    {
      "type": "image_picker",
      "id": "image_2",
      "label": "Secondary image",
      "info": "Displays both images at a 50% width. 3200 x 1200px .jpg recommended. [Learn more](https://invisiblethemes.com/link/broadcast/images)"
    },
    {
      "type": "url",
      "id": "link",
      "label": "Image link"
    },
    {
      "type": "header",
      "content": "Layout"
    },
    {
      "type": "select",
      "id": "width",
      "label": "Width",
      "default": "wrapper--full",
      "options": [
        {"value": "wrapper--full", "label": "Full width"},
        {"value": "wrapper--full-padded", "label": "Full width padded"}
      ]
    },
    {
      "type": "header",
      "content": "Desktop"
    },
    {
      "type": "select",
      "id": "height",
      "label": "Height",
      "default": "screen-height-three-quarters",
      "options": [
        {"value": "image-height", "label": "Image height"},
        {"value": "screen-height-full", "label": "Full screen height"},
        {"value": "screen-height-three-quarters", "label": "3/4 of screen"},
        {"value": "screen-height-two-thirds", "label": "2/3 of screen"},
        {"value": "screen-height-one-half", "label": "1/2 of screen"},
        {"value": "screen-height-one-third", "label": "1/3 of screen"},
        {"value": "seven-fifty-height-hero", "label": "750px"},
        {"value": "sixty-fifty-height-hero", "label": "650px"},
        {"value": "five-fifty-height-hero ", "label": "550px"},
        {"value": "four-fifty-height-hero", "label": "450px"}
      ]
    },
    {
      "type": "select",
      "id": "flex_align_desktop",
      "label": "Desktop alignment",
      "default": "align--middle-center-desktop",
      "options": [
        {"value": "align--top-left-desktop", "label": "Top left"},
        {"value": "align--top-center-desktop", "label": "Top center"},
        {"value": "align--top-right-desktop", "label": "Top right"},
        {"value": "align--middle-left-desktop", "label": "Middle left"},
        {"value": "align--middle-center-desktop", "label": "Absolute center"},
        {"value": "align--middle-right-desktop", "label": "Middle right"},
        {"value": "align--bottom-left-desktop", "label": "Bottom left"},
        {"value": "align--bottom-center-desktop", "label": "Bottom center"},
        {"value": "align--bottom-right-desktop", "label": "Bottom right"}
      ]
    },
    {
      "type": "range",
      "id": "content_width",
      "min": 20,
      "max": 100,
      "step": 1,
      "unit": "%",
      "label": "Content width",
      "default": 50
    },
    {
      "type": "header",
      "content": "Mobile"
    },
    {
      "type": "image_picker",
      "id": "mobile_image",
      "label": "Image",
      "info": "1200 x 1600px .jpg recommended. [Learn more](https://invisiblethemes.com/link/broadcast/images)"
    },
    {
      "type": "select",
      "id": "mobile_height",
      "label": "Height",
      "default": "screen-height-three-quarters--mobile",
      "options": [
        {"value": "image-height--mobile", "label": "Image height"},
        {"value": "screen-height-full--mobile", "label": "Full screen height"},
        {"value": "screen-height-three-quarters--mobile", "label": "3/4 of screen"},
        {"value": "screen-height-two-thirds--mobile", "label": "2/3 of screen"},
        {"value": "screen-height-one-half--mobile", "label": "1/2 of screen"},
        {"value": "screen-height-one-third--mobile", "label": "1/3 of screen"},
        {"value": "seven-fifty-height-hero--mobile", "label": "750px"},
        {"value": "sixty-fifty-height-hero--mobile", "label": "650px"},
        {"value": "five-fifty-height-hero--mobile", "label": "550px"},
        {"value": "four-fifty-height-hero--mobile", "label": "450px"}
      ]
    },
    {
      "type": "select",
      "id": "flex_align_mobile",
      "label": "Mobile alignment",
      "default": "align--middle-center-mobile",
      "options": [
        {"value": "align--top-left-mobile", "label": "Top left"},
        {"value": "align--top-center-mobile", "label": "Top center"},
        {"value": "align--top-right-mobile", "label": "Top right"},
        {"value": "align--middle-left-mobile", "label": "Middle left"},
        {"value": "align--middle-center-mobile", "label": "Absolute center"},
        {"value": "align--middle-right-mobile", "label": "Middle right"},
        {"value": "align--bottom-left-mobile", "label": "Bottom left"},
        {"value": "align--bottom-center-mobile", "label": "Bottom center"},
        {"value": "align--bottom-right-mobile", "label": "Bottom right"}
      ]
    },
    {
      "type": "header",
      "content": "Colors"
    },
    {
      "type": "color_scheme",
      "id": "color_scheme",
      "default": "scheme_1",
      "label": "Color scheme"
    },
    {
      "type": "checkbox",
      "id": "show_text_background",
      "label": "Show text background",
      "default": false
    },
    {
      "type": "range",
      "id": "overlay_opacity",
      "label": "Overlay opacity",
      "info": "Increase contrast for legible text.",
      "min": 0,
      "max": 100,
      "step": 5,
      "unit": "%",
      "default": 0
    },
    {
      "type": "checkbox",
      "id": "show_overlay_text",
      "label": "Overlay behind text only",
      "default": false,
      "info": "Overlay opacity must be greater than 0."
    },
    {
      "type": "header",
      "content": "Padding"
    },
    {
      "type": "range",
      "id": "padding_top",
      "min": 0,
      "max": 200,
      "step": 2,
      "unit": "px",
      "label": "Top",
      "default": 0
    },
    {
      "type": "range",
      "id": "padding_bottom",
      "min": 0,
      "max": 200,
      "step": 2,
      "unit": "px",
      "label": "Bottom",
      "default": 0
    },
    {
      "type": "header",
      "content": "Advanced"
    },
    {
      "type": "checkbox",
      "id": "show_placeholder",
      "label": "Show placeholder image",
      "info": "Disable if using a metafield",
      "default": true
    }
  ],
  "max_blocks": 5,
  "blocks": [
    {
      "type": "heading",
      "name": "Heading",
      "limit": 2,
      "settings": [
        {
          "type": "header",
          "content": "Typography"
        },
        {
          "type": "text",
          "id": "title",
          "label": "Heading",
          "default": "Image banner"
        },
        {
          "type": "select",
          "id": "heading_font_size",
          "label": "Heading size",
          "default": "heading-x-large",
          "options": [
            {"value": "heading-mini", "label": "Mini"},
            {"value": "heading-x-small", "label": "Extra small"},
            {"value": "heading-small", "label": "Small"},
            {"value": "heading-medium", "label": "Medium"},
            {"value": "heading-large", "label": "Large"},
            {"value": "heading-x-large", "label": "Extra large"}
          ]
        },
        {
          "type": "header",
          "content": "Advanced"
        },
        {
          "type": "select",
          "id": "heading_tag",
          "label": "Heading SEO tag",
          "default": "automatic",
          "options": [
            {"value": "automatic", "label": "Automatic"},
            {"value": "h1", "label": "H1"},
            {"value": "h2", "label": "H2"},
            {"value": "h3", "label": "H3"},
            {"value": "h4", "label": "H4"},
            {"value": "h5", "label": "H5"},
            {"value": "h6", "label": "H6"}
          ]
        }
      ]
    },
    {
      "type": "text",
      "name": "Text",
      "limit": 2,
      "settings": [
        {
          "type": "richtext",
          "id": "text",
          "label": "Text",
          "default": "<p>Tell your brand's story through images.<\/p>"
        },
        {
          "type": "select",
          "id": "text_font_size",
          "label": "Text size",
          "info": "Automatically generated by the base size. Applies only when content in Text field is set to Paragraph.",
          "default": "body-large",
          "options": [
            {"value": "body-x-small", "label": "Extra small"},
            {"value": "body-small", "label": "Small"},
            {"value": "body-medium", "label": "Medium"},
            {"value": "body-large", "label": "Large"},
            {"value": "body-x-large", "label": "Extra large"}
          ]
        }
      ]
    },
    {
      "type": "button",
      "name": "Button",
      "limit": 2,
      "settings": [
        {
          "type": "text",
          "id": "link_text",
          "label": "Text",
          "default": "View products"
        },
        {
          "type": "url",
          "id": "link",
          "label": "Link"
        },
        {
          "type": "select",
          "id": "button_type",
          "label": "Color",
          "default": "btn--primary",
          "options": [
            {"value": "btn--black", "label": "Black"},
            {"value": "btn--white", "label": "White"},
            {"value": "btn--primary", "label": "Primary"},
            {"value": "btn--secondary", "label": "Secondary"}
          ]
        },
        {
          "type": "select",
          "id": "button_size",
          "label": "Size",
          "default": "",
          "options": [
            {"label": "Small", "value": "btn--small"},
            {"label": "Medium", "value": ""},
            {"label": "Large", "value": "btn--large"}
          ]
        },
        {
          "type": "select",
          "id": "button_style",
          "label": "Style",
          "default": "btn--solid",
          "options": [
            {"label": "Solid", "value": "btn--solid"},
            {"label": "Outline", "value": "btn--outline"},
            {"label": "Text", "value": "btn--text"}
          ]
        },
        {
          "type": "checkbox",
          "id": "show_arrow",
          "label": "Show arrow",
          "default": false
        }
      ]
    },
    {
      "type": "code",
      "name": "Custom code",
      "settings": [
        {
          "type": "liquid",
          "id": "code",
          "label": "Custom code",
          "default": "<p>Once you write some custom code, it will render right here.</p>"
        }
      ]
    }
  ],
  "presets": [
    {
      "name": "Image banner",
      "blocks": [
        {
          "type": "heading"
        },
        {
          "type": "text"
        },
        {
          "type": "button"
        }
      ]
    }
  ],
  "disabled_on": {
    "groups": ["header", "aside", "footer"]
  }
}
{% endschema %}
