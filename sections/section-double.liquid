<!-- /sections/section-double.liquid -->

{%- render 'section-double' -%}

{% schema %}
{
  "name": "Image with text",
  "class": "section-overlay-header",
  "settings": [
    {
      "type": "header",
      "content": "Content"
    },
    {
      "type": "image_picker",
      "id": "image_1",
      "label": "Primary image",
      "info": "2000 x 1400px .jpg recommended. [Learn more](https://invisiblethemes.com/link/broadcast/images)"
    },
    {
      "type": "image_picker",
      "id": "image_2",
      "label": "Secondary image",
      "info": "Displays both images at a 50% width. 2000 x 1400px .jpg recommended. [Learn more](https://invisiblethemes.com/link/broadcast/images)"
    },
    {
      "type": "select",
      "id": "image_width",
      "label": "Image width",
      "default": "one-half",
      "options": [
        {"value": "one-half", "label": "50%"},
        {"value": "three-quarters", "label": "75%"}
      ]
    },
    {
      "type": "header",
      "content": "Typography"
    },
    {
      "type": "select",
      "id": "text_alignment",
      "label": "Text alignment",
      "default": "text-left",
      "options": [
        {"value": "text-left", "label": "Left"},
        {"value": "text-center", "label": "Centered"}
      ]
    },
    {
      "type": "header",
      "content": "Layout"
    },
    {
      "type": "select",
      "id": "layout",
      "label": "Layout",
      "default": "",
      "options": [
        {"value": "", "label": "Image left, text right"},
        {"value": "is-reversed", "label": "Text left, image right"}
      ]
    },
    {
      "type": "select",
      "id": "width",
      "label": "Width",
      "default": "wrapper--full",
      "options": [
        {"value": "wrapper--full", "label": "Full width"},
        {"value": "wrapper--full-padded", "label": "Full width padded"},
        {"value": "wrapper", "label": "Normal"}
      ]
    },
    {
      "type": "header",
      "content": "Desktop"
    },
    {
      "type": "select",
      "id": "height",
      "label": "Height",
      "default": "screen-height-three-quarters",
      "options": [
        {"value": "image-height", "label": "Image height"},
        {"value": "screen-height-full", "label": "Full screen height"},
        {"value": "screen-height-three-quarters", "label": "3/4 of screen"},
        {"value": "screen-height-two-thirds", "label": "2/3 of screen"},
        {"value": "screen-height-one-half", "label": "1/2 of screen"},
        {"value": "screen-height-one-third", "label": "1/3 of screen"},
        {"value": "seven-fifty-height-hero", "label": "750px"},
        {"value": "sixty-fifty-height-hero", "label": "650px"},
        {"value": "five-fifty-height-hero", "label": "550px"},
        {"value": "four-fifty-height-hero", "label": "450px"}
      ]
    },
    {
      "type": "header",
      "content": "Mobile"
    },
    {
      "type": "select",
      "id": "mobile_height",
      "label": "Height",
      "default": "screen-height-one-half--mobile",
      "options": [
        {"value": "image-height--mobile", "label": "Image height"},
        {"value": "screen-height-full--mobile", "label": "Full screen height"},
        {"value": "screen-height-three-quarters--mobile", "label": "3/4 of screen"},
        {"value": "screen-height-two-thirds--mobile", "label": "2/3 of screen"},
        {"value": "screen-height-one-half--mobile", "label": "1/2 of screen"},
        {"value": "screen-height-one-third--mobile", "label": "1/3 of screen"},
        {"value": "seven-fifty-height-hero--mobile", "label": "750px"},
        {"value": "sixty-fifty-height-hero--mobile", "label": "650px"},
        {"value": "five-fifty-height-hero--mobile", "label": "550px"},
        {"value": "four-fifty-height-hero--mobile", "label": "450px"}
      ]
    },
    {
      "type": "checkbox",
      "id": "reverse_blocks",
      "label": "Reverse block placement",
      "default": false
    },
    {
      "type": "header",
      "content": "Colors"
    },
    {
      "type": "color_scheme",
      "id": "color_scheme",
      "default": "scheme_1",
      "label": "Color scheme"
    },
    {
      "type": "header",
      "content": "Padding"
    },
    {
      "type": "range",
      "id": "padding_top",
      "min": 0,
      "max": 200,
      "step": 2,
      "unit": "px",
      "label": "Top",
      "default": 0
    },
    {
      "type": "range",
      "id": "padding_bottom",
      "min": 0,
      "max": 200,
      "step": 2,
      "unit": "px",
      "label": "Bottom",
      "default": 0
    },
    {
      "type": "header",
      "content": "Advanced"
    },
    {
      "type": "checkbox",
      "id": "show_placeholder",
      "label": "Show placeholder image",
      "info": "Disable if using a metafield",
      "default": true
    }
  ],
  "blocks": [
    {
      "type": "heading",
      "name": "Heading",
      "settings": [
        {
          "type": "text",
          "id": "title",
          "label": "Heading",
          "default": "Image with text"
        },
        {
          "type": "select",
          "id": "heading_font_size",
          "label": "Heading size",
          "default": "heading-large",
          "options": [
            {"value": "heading-mini", "label": "Mini"},
            {"value": "heading-x-small", "label": "Extra small"},
            {"value": "heading-small", "label": "Small"},
            {"value": "heading-medium", "label": "Medium"},
            {"value": "heading-large", "label": "Large"},
            {"value": "heading-x-large", "label": "Extra large"}
          ]
        },
        {
          "type": "header",
          "content": "Padding"
        },
        {
          "type": "range",
          "id": "padding_bottom",
          "min": 0,
          "max": 50,
          "step": 2,
          "unit": "px",
          "label": "Bottom",
          "default": 16
        },
        {
          "type": "header",
          "content": "Advanced"
        },
        {
          "type": "select",
          "id": "heading_tag",
          "label": "Heading SEO tag",
          "default": "automatic",
          "options": [
            {"value": "automatic", "label": "Automatic"},
            {"value": "h1", "label": "H1"},
            {"value": "h2", "label": "H2"},
            {"value": "h3", "label": "H3"},
            {"value": "h4", "label": "H4"},
            {"value": "h5", "label": "H5"},
            {"value": "h6", "label": "H6"}
          ]
        }
      ]
    },
    {
      "type": "subheading",
      "name": "Subheading",
      "settings": [
        {
          "type": "text",
          "id": "subheading",
          "label": "Subheading",
          "default": "Introducing"
        },
        {
          "type": "header",
          "content": "Padding"
        },
        {
          "type": "range",
          "id": "padding_bottom",
          "min": 0,
          "max": 50,
          "step": 2,
          "unit": "px",
          "label": "Bottom",
          "default": 16
        }
      ]
    },
    {
      "type": "text",
      "name": "Text",
      "settings": [
        {
          "type": "richtext",
          "id": "text",
          "label": "Text",
          "default": "<p>Use this text to share information about your brand with your customers. Describe a product, share announcements, or welcome customers to your store.</p>"
        },
        {
          "type": "select",
          "id": "text_font_size",
          "label": "Text size",
          "info": "Automatically generated by the base size. Applies only when content in Text field is set to Paragraph.",
          "default": "body-medium",
          "options": [
            {"value": "body-x-small", "label": "Extra small"},
            {"value": "body-small", "label": "Small"},
            {"value": "body-medium", "label": "Medium"},
            {"value": "body-large", "label": "Large"},
            {"value": "body-x-large", "label": "Extra large"}
          ]
        },
        {
          "type": "header",
          "content": "Padding"
        },
        {
          "type": "range",
          "id": "padding_bottom",
          "min": 0,
          "max": 50,
          "step": 2,
          "unit": "px",
          "label": "Bottom",
          "default": 16
        }
      ]
    },
    {
      "type": "buttons",
      "name": "Button",
      "settings": [
        {
          "type": "text",
          "id": "button_text",
          "label": "Text",
          "default": "Learn more"
        },
        {
          "type": "url",
          "id": "button_url",
          "label": "Link"
        },
        {
          "type": "select",
          "id": "button_type",
          "label": "Color",
          "default": "btn--primary",
          "options": [
            {"value": "btn--black", "label": "Black"},
            {"value": "btn--white", "label": "White"},
            {"value": "btn--primary", "label": "Primary"},
            {"value": "btn--secondary", "label": "Secondary"}
          ]
        },
        {
          "type": "select",
          "id": "button_size",
          "label": "Size",
          "default": "",
          "options": [
            {"label": "Small", "value": "btn--small"},
            {"label": "Medium", "value": ""},
            {"label": "Large", "value": "btn--large"}
          ]
        },
        {
          "type": "select",
          "id": "button_style",
          "label": "Style",
          "default": "btn--solid",
          "options": [
            {"label": "Solid", "value": "btn--solid"},
            {"label": "Outline", "value": "btn--outline"},
            {"label": "Text", "value": "btn--text"}
          ]
        },
        {
          "type": "checkbox",
          "id": "show_arrow",
          "label": "Show arrow",
          "default": false
        },
        {
          "type": "header",
          "content": "Padding"
        },
        {
          "type": "range",
          "id": "padding_bottom",
          "min": 0,
          "max": 50,
          "step": 2,
          "unit": "px",
          "label": "Bottom",
          "default": 16
        }
      ]
    },
    {
      "type": "divider",
      "name": "Divider",
      "settings": [
        {
          "type": "checkbox",
          "id": "show_line",
          "label": "Show line",
          "default": true
        },
        {
          "type": "header",
          "content": "Padding"
        },
        {
          "type": "range",
          "id": "padding_bottom",
          "min": 0,
          "max": 50,
          "step": 1,
          "unit": "px",
          "label": "Bottom",
          "default": 20
        }
      ]
    },
    {
      "name": "Accordion",
      "type": "accordion",
      "settings": [
        {
          "type": "text",
          "id": "title",
          "label": "Question",
          "default": "Frequently asked question"
        },
        {
          "type": "richtext",
          "id": "content",
          "label": "Answer",
          "default": "<p>Share details about your shipping policies, item returns, or customer service.<\/p>"
        },
        {
          "type": "header",
          "content": "Icon"
        },
        {
          "type": "checkbox",
          "id": "show_icon",
          "label": "Show icon",
          "default": false
        },
        {
          "type": "select",
          "id": "icon_name",
          "label": "Icon",
          "default": "icon-award",
          "options": [
            {"label": "Award", "value": "icon-award"},
            {"label": "Box", "value": "icon-box"},
            {"label": "Chat", "value": "icon-chat"},
            {"label": "Check", "value": "icon-check"},
            {"label": "Check circle", "value": "icon-check-circle"},
            {"label": "Cloud", "value": "icon-cloud"},
            {"label": "Diameter", "value": "icon-diameter"},
            {"label": "Discount", "value": "icon-discount"},
            {"label": "Donation", "value": "icon-donation"},
            {"label": "Droplet", "value": "icon-droplet"},
            {"label": "Info", "value": "icon-info-empty"},
            {"label": "Email", "value": "icon-email"},
            {"label": "Fast shipment", "value": "icon-fast-shipment"},
            {"label": "Flare", "value": "icon-flare"},
            {"label": "Flower", "value": "icon-flower"},
            {"label": "Gift", "value": "icon-gift"},
            {"label": "Green shipment", "value": "icon-green-shipment"},
            {"label": "Heart", "value": "icon-heart"},
            {"label": "Leaf", "value": "icon-leaf"},
            {"label": "Lightning", "value": "icon-lightning"},
            {"label": "Location", "value": "icon-location"},
            {"label": "Mail", "value": "icon-mail"},
            {"label": "Notes", "value": "icon-notes"},
            {"label": "Pants", "value": "icon-pants"},
            {"label": "Peace", "value": "icon-peace"},
            {"label": "Pin", "value": "icon-pin"},
            {"label": "Planet", "value": "icon-planet"},
            {"label": "Phone", "value": "icon-phone"},
            {"label": "Recycle", "value": "icon-recycle"},
            {"label": "Ruler", "value": "icon-ruler"},
            {"label": "Shield", "value": "icon-shield"},
            {"label": "Smile", "value": "icon-smile"},
            {"label": "Star", "value": "icon-star"},
            {"label": "Tree", "value": "icon-tree"},
            {"label": "Trophy", "value": "icon-trophy"},
            {"label": "Truck", "value": "icon-truck"},
            {"label": "Vegan", "value": "icon-vegan"},
            {"label": "Wash", "value": "icon-wash"},
            {"label": "Washing machine", "value": "icon-washing-machine"}
          ]
        },
        {
          "type": "image_picker",
          "id": "icon_image",
          "label": "Alternative icon/image",
          "info": "160 x 160px .svg recommended. [Learn more](https://invisiblethemes.com/link/broadcast/images)"
        },
        {
          "type": "range",
          "id": "icon_size",
          "label": "Size",
          "unit": "px",
          "min": 20,
          "max": 80,
          "step": 5,
          "default": 20
        },
        {
          "type": "color",
          "id": "icon_color",
          "label": "Icon color"
        },
        {
          "type": "header",
          "content": "Padding"
        },
        {
          "type": "range",
          "id": "padding_bottom",
          "min": 0,
          "max": 50,
          "step": 2,
          "unit": "px",
          "label": "Bottom",
          "default": 16
        }
      ]
    },
    {
      "type": "image",
      "name": "Image",
      "settings": [
        {
          "type": "image_picker",
          "id": "image",
          "label": "Image",
          "info": "2000 x 1400px .jpg recommended. [Learn more](https://invisiblethemes.com/link/broadcast/images)"
        },
        {
          "type": "range",
          "id": "image_width",
          "min": 20,
          "max": 100,
          "step": 5,
          "unit": "%",
          "label": "Width",
          "default": 100
        },
        {
          "type": "header",
          "content": "Padding"
        },
        {
          "type": "range",
          "id": "padding_bottom",
          "min": 0,
          "max": 50,
          "step": 2,
          "unit": "px",
          "label": "Bottom",
          "default": 16
        }
      ]
    },
    {
      "type": "video",
      "name": "Video",
      "settings": [
        {
          "type": "video",
          "id": "video",
          "label": "Video"
        },
        {
          "type": "range",
          "id": "video_width",
          "min": 50,
          "max": 100,
          "step": 5,
          "unit": "%",
          "label": "Width",
          "default": 100
        },
        {
          "type": "checkbox",
          "id": "custom_aspect_ratio",
          "label": "Set custom aspect ratio",
          "default": false
        },
        {
          "type": "range",
          "id": "aspect_ratio",
          "min": 0.5,
          "max": 1.5,
          "step": 0.1,
          "unit": ":1",
          "label": "Aspect ratio",
          "info": "Wide to tall",
          "default": 1
        },
        {
          "type": "header",
          "content": "Padding"
        },
        {
          "type": "range",
          "id": "padding_bottom",
          "min": 0,
          "max": 50,
          "step": 2,
          "unit": "px",
          "label": "Bottom",
          "default": 16
        }
      ]
    },
    {
      "type": "product",
      "name": "Product",
      "settings": [
        {
          "type": "product_list",
          "id": "product_list",
          "label": "Product list",
          "limit": 2,
          "info": "Choose up to 2 products"
        },
        {
          "type": "checkbox",
          "id": "show_product_card",
          "label": "Show product card",
          "default": true
        },
        {
          "type": "header",
          "content": "Padding"
        },
        {
          "type": "range",
          "id": "padding_bottom",
          "min": 0,
          "max": 50,
          "step": 2,
          "unit": "px",
          "label": "Bottom",
          "default": 16
        }
      ]
    },
    {
      "type": "collection",
      "name": "Collection",
      "settings": [
        {
          "type": "checkbox",
          "id": "show_collection_card",
          "label": "Show collection card",
          "default": true
        },
        {
          "type": "header",
          "content": "Primary collection"
        },
        {
          "label": "Primary collection",
          "id": "collection_1",
          "type": "collection"
        },
        {
          "type": "image_picker",
          "id": "image_1",
          "label": "Image",
          "info": "1400 x 1400px .jpg recommended. [Learn more](https://invisiblethemes.com/link/broadcast/images)"
        },
        {
          "type": "text",
          "id": "button_text_1",
          "label": "Button text"
        },
        {
          "type": "header",
          "content": "Secondary collection"
        },
        {
          "label": "Secondary collection",
          "id": "collection_2",
          "type": "collection"
        },
        {
          "type": "image_picker",
          "id": "image_2",
          "label": "Image",
          "info": "1400 x 1400px .jpg recommended. [Learn more](https://invisiblethemes.com/link/broadcast/images)"
        },
        {
          "type": "header",
          "content": "Button"
        },
        {
          "type": "text",
          "id": "button_text_2",
          "label": "Button text"
        },
        {
          "type": "select",
          "id": "button_type",
          "label": "Color",
          "default": "btn--primary",
          "options": [
            {"value": "btn--black", "label": "Black"},
            {"value": "btn--white", "label": "White"},
            {"value": "btn--primary", "label": "Primary"},
            {"value": "btn--secondary", "label": "Secondary"}
          ]
        },
        {
          "type": "select",
          "id": "button_size",
          "label": "Size",
          "default": "",
          "options": [
            {"label": "Small", "value": "btn--small"},
            {"label": "Medium", "value": ""},
            {"label": "Large", "value": "btn--large"}
          ]
        },
        {
          "type": "select",
          "id": "button_style",
          "label": "Style",
          "default": "btn--solid",
          "options": [
            {"label": "Solid", "value": "btn--solid"},
            {"label": "Outline", "value": "btn--outline"},
            {"label": "Text", "value": "btn--text"}
          ]
        },
        {
          "type": "checkbox",
          "id": "show_arrow",
          "label": "Show arrow",
          "default": false
        },
        {
          "type": "header",
          "content": "Padding"
        },
        {
          "type": "range",
          "id": "padding_bottom",
          "min": 0,
          "max": 50,
          "step": 2,
          "unit": "px",
          "label": "Bottom",
          "default": 16
        }
      ]
    },
    {
      "type": "code",
      "name": "Custom code",
      "settings": [
        {
          "type": "liquid",
          "id": "code",
          "label": "Custom code",
          "default": "<p>Once you write some custom code, it will render right here.</p>"
        },
        {
          "type": "header",
          "content": "Padding"
        },
        {
          "type": "range",
          "id": "padding_bottom",
          "min": 0,
          "max": 50,
          "step": 2,
          "unit": "px",
          "label": "Bottom",
          "default": 16
        }
      ]
    }
  ],
  "presets": [
    {
      "name": "Image with text",
      "blocks": [
        {
          "type": "subheading"
        },
        {
          "type": "heading"
        },
        {
          "type": "text"
        },
        {
          "type": "buttons"
        }
      ]
    }
  ],
  "disabled_on": {
    "groups": ["header", "aside", "footer"]
  }
}
{% endschema %}
