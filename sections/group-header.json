/*
 * ------------------------------------------------------------
 * IMPORTANT: The contents of this file are auto-generated.
 *
 * This file may be updated by the Shopify admin theme editor
 * or related systems. Please exercise caution as any changes
 * made to this file may be overwritten.
 * ------------------------------------------------------------
 */
{
  "type": "header",
  "name": "Header",
  "sections": {
    "announcement": {
      "type": "announcement",
      "blocks": {
        "text": {
          "type": "text",
          "settings": {
            "text": "<p>Free shipping over $50</p>",
            "target_url_enabled": false,
            "target_url": "shopify://collections/all",
            "target_referrer_enabled": false,
            "target_referrer": "",
            "target_device_enabled": false,
            "target_device": "mobile"
          }
        },
        "text-2": {
          "type": "text",
          "settings": {
            "text": "<p><a href=\"/collections/all\" title=\"All products\">New arrivals are here! Take a peek now</a></p>",
            "target_url_enabled": false,
            "target_url": "",
            "target_referrer_enabled": false,
            "target_referrer": "",
            "target_device_enabled": false,
            "target_device": "mobile"
          }
        }
      },
      "block_order": [
        "text",
        "text-2"
      ],
      "settings": {
        "show_border": false,
        "layout": "slider",
        "slider_speed": 7,
        "text_alignment": "center",
        "show_arrows": true,
        "marquee_speed": 100,
        "message_spacing": 10,
        "text_font_size": "body-small",
        "color_scheme": "scheme-2",
        "border_color": "",
        "padding_top": 0,
        "padding_bottom": 0
      }
    },
    "header": {
      "type": "header",
      "blocks": {
        "toolbar": {
          "type": "toolbar",
          "disabled": true,
          "settings": {
            "text": "",
            "menu": "",
            "show_border": true,
            "border_color": "",
            "border_opacity": 20,
            "show_social_links": true,
            "show_gift_card": true,
            "show_locale_selector": true,
            "show_globe_icon": true,
            "show_country_selector": true,
            "show_country_name": false,
            "show_country_flag": false,
            "color_scheme": "scheme-1",
            "padding_top": 4,
            "padding_bottom": 4
          }
        }
      },
      "block_order": [
        "toolbar"
      ],
      "settings": {
        "header_sticky": true,
        "header_style": "logo_beside",
        "show_separator_line": false,
        "header_border_color": "",
        "separator_opacity": 35,
        "show_icons": true,
        "icon_as_circle": false,
        "cart_icon_family": "bag",
        "logo": "shopify://shop_images/Transparent_logo.png",
        "logo_max_limit": 170,
        "logo_max_limit_mobile": 190,
        "main_menu_linklist": "",
        "mobile_menu_linklist": "",
        "enable_search": true,
        "show_social_icons": false,
        "button_text": "",
        "button_link": "",
        "button_type": "btn--black",
        "show_locale_selector": false,
        "show_globe_icon": true,
        "show_country_selector": false,
        "show_country_name": false,
        "show_country_flag": false,
        "highlight_item": "Sale",
        "highlight_item_color": "#d02e2e",
        "transparent_home": false,
        "transparent_collection": false,
        "transparent_article": false,
        "transparent_page": false,
        "transparent_product_page": false,
        "secondary_menu_linklist": "",
        "tertiary_menu_linklist": "",
        "padding_top": 15,
        "padding_bottom": 15
      }
    },
    "mobile-menu": {
      "type": "mobile-menu",
      "blocks": {
        "menu": {
          "type": "menu",
          "settings": {
            "mobile_menu_linklist": "",
            "text_font_size": "body-medium",
            "highlight_item": "Sale",
            "highlight_item_color": "#d02e2e",
            "padding": true,
            "padding_bottom": 14
          }
        },
        "divider-1": {
          "type": "divider",
          "settings": {
            "show_line": true,
            "padding_bottom": 8
          }
        },
        "text": {
          "type": "text",
          "settings": {
            "text": "Shop our new arrivals:",
            "align_text": "text-left",
            "text_font_size": "body-medium",
            "padding_bottom": 14
          }
        },
        "product-1": {
          "type": "product",
          "settings": {
            "product": "curl-defining-styling-formula",
            "image_width": 15,
            "text_font_size": "body-small",
            "bg_color": "",
            "padding": false,
            "pin_to_bottom": false,
            "padding_bottom": 14
          }
        },
        "product-2": {
          "type": "product",
          "settings": {
            "product": "moisture-lock-leave-in-treatment",
            "image_width": 15,
            "text_font_size": "body-small",
            "bg_color": "",
            "padding": false,
            "pin_to_bottom": false,
            "padding_bottom": 14
          }
        },
        "divider-2": {
          "type": "divider",
          "settings": {
            "show_line": true,
            "padding_bottom": 8
          }
        },
        "socials": {
          "type": "socials",
          "settings": {
            "width": "full",
            "pin_to_bottom": false,
            "padding_bottom": 10
          }
        },
        "divider-3": {
          "type": "divider",
          "settings": {
            "show_line": true,
            "padding_bottom": 14
          }
        },
        "localization": {
          "type": "localization",
          "settings": {
            "show_locale_selector": true,
            "show_globe_icon": true,
            "show_country_selector": true,
            "show_country_name": false,
            "show_country_flag": true,
            "width": "full",
            "pin_to_bottom": true,
            "padding_bottom": 14
          }
        }
      },
      "block_order": [
        "menu",
        "divider-1",
        "text",
        "product-1",
        "product-2",
        "divider-2",
        "socials",
        "divider-3",
        "localization"
      ],
      "settings": {
        "logo": "{{ shop.brand.logo }}",
        "logo_max_limit": 120,
        "color_scheme": "scheme-1"
      }
    }
  },
  "order": [
    "announcement",
    "header",
    "mobile-menu"
  ]
}
