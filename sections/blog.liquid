<!-- /sections/blog.liquid -->
{%- liquid
  assign articles_per_row = section.settings.grid
  assign article_limit = articles_per_row | times: section.settings.rows
  assign color_scheme = 'color-' | append: section.settings.color_scheme
-%}

{%- style -%}
  #Blog--{{ section.id }} {
    --PT: {{ section.settings.padding_top }}px;
    --PB: {{ section.settings.padding_bottom }}px;

    --COLUMNS: {{ articles_per_row }};
    --COLUMNS-MEDIUM: {{ 3 | at_most: articles_per_row }};
    --COLUMNS-SMALL: {{ 2 | at_most: articles_per_row }};
    --COLUMNS-MOBILE: 1;
  }
{%- endstyle -%}

<section
  id="Blog--{{ section.id }}"
  class="blog-section section-padding {{ color_scheme }}"
  data-section-id="{{ section.id }}"
  data-section-type="blog"
>
  <div class="wrapper--full-padded">
    {%- if section.settings.show_title or section.settings.show_rss -%}
      <h1 class="blog__title h2">
        {%- if section.settings.show_title -%}
          {{ blog.title }}
        {%- endif -%}
        {%- if section.settings.show_rss -%}
          <a class="icon-rss" href="{{ blog.title }}" rel="noopener" target="_blank">
            {%- render 'icon-rss' -%}
          </a>
        {%- endif -%}
      </h1>
    {%- endif -%}

    {%- if current_tags and section.settings.show_tag_nav == false -%}
      <h2 class="blog__nav h5">
        <a href="{{ blog.url }}" title="{{ 'blogs.article.all' | t }}" class="btn btn--black">
          {{ current_tags.first }}
          {%- render 'icon-cancel' -%}
        </a>
      </h2>
    {%- endif -%}

    {%- if section.settings.show_tag_nav -%}
      <ul class="inline-nav capitalize">
        <li
          {% if current_tags == blank %}
            class="inline-nav--active"
          {% endif %}
        >
          <a href="{{ blog.url }}">{{ 'blogs.article.all' | t }}</a>
        </li>
        {%- for tag in blog.all_tags -%}
          <li
            {% if current_tags contains tag %}
              class="inline-nav--active"
            {% endif %}
          >
            {{ tag | link_to_tag: tag }}
          </li>
        {%- endfor -%}
      </ul>
    {%- endif -%}

    {%- paginate blog.articles by article_limit -%}
      {%- render 'blog-posts', blog_articles: blog.articles, is_blog_page: true -%}

      {%- render 'pagination', paginate: paginate -%}
    {%- endpaginate -%}

    {%- if blog.articles_count == 0 -%}
      <div class="collection-error centered">
        <div class="no-products h4 text-center">{{ 'blogs.article.no_articles' | t }}</div>
      </div>
    {%- endif -%}
  </div>
</section>

{% schema %}
{
  "name": "Blog pages",
  "settings": [
    {
      "type": "checkbox",
      "id": "show_title",
      "label": "Show title",
      "default": true
    },
    {
      "type": "checkbox",
      "id": "show_rss",
      "label": "Show RSS icon",
      "default": false
    },
    {
      "type": "checkbox",
      "id": "show_tag_nav",
      "label": "Show tag navigation",
      "default": false
    },
    {
      "type": "header",
      "content": "Articles"
    },
    {
      "type": "range",
      "id": "aspect_ratio",
      "min": 0.5,
      "max": 1.5,
      "step": 0.1,
      "unit": ":1",
      "label": "Article image aspect ratio",
      "info": "Wide to tall",
      "default": 1
    },
    {
      "type": "select",
      "id": "title_font_size",
      "label": "Title size",
      "default": "heading-small",
      "options": [
        {"value": "heading-mini", "label": "Mini"},
        {"value": "heading-x-small", "label": "Extra small"},
        {"value": "heading-small", "label": "Small"},
        {"value": "heading-medium", "label": "Medium"},
        {"value": "heading-large", "label": "Large"},
        {"value": "heading-x-large", "label": "Extra large"}
      ]
    },
    {
      "type": "checkbox",
      "id": "show_tags",
      "label": "Show tags",
      "default": false
    },
    {
      "type": "checkbox",
      "id": "show_author",
      "label": "Show author",
      "default": false
    },
    {
      "type": "checkbox",
      "id": "show_date",
      "label": "Show date",
      "default": false
    },
    {
      "type": "checkbox",
      "id": "show_button",
      "label": "Show 'Read more' button",
      "default": false
    },
    {
      "type": "checkbox",
      "id": "show_comments",
      "label": "Show comment count",
      "default": false
    },
    {
      "type": "header",
      "content": "Layout"
    },
    {
      "type": "range",
      "id": "grid",
      "min": 1,
      "max": 4,
      "step": 1,
      "label": "Articles per row",
      "default": 4
    },
    {
      "type": "range",
      "id": "rows",
      "min": 1,
      "max": 5,
      "step": 1,
      "label": "Number of rows",
      "default": 4
    },
    {
      "type": "header",
      "content": "Colors"
    },
    {
      "type": "color_scheme",
      "id": "color_scheme",
      "default": "scheme_1",
      "label": "Color scheme"
    },
    {
      "type": "header",
      "content": "Padding"
    },
    {
      "type": "range",
      "id": "padding_top",
      "min": 0,
      "max": 100,
      "step": 1,
      "unit": "px",
      "label": "Top",
      "default": 32
    },
    {
      "type": "range",
      "id": "padding_bottom",
      "min": 0,
      "max": 100,
      "step": 1,
      "unit": "px",
      "label": "Bottom",
      "default": 0
    },
    {
      "type": "header",
      "content": "Advanced"
    },
    {
      "type": "select",
      "id": "title_tag",
      "label": "Title SEO tag",
      "default": "automatic",
      "options": [
        {"value": "automatic", "label": "Automatic"},
        {"value": "h1", "label": "H1"},
        {"value": "h2", "label": "H2"},
        {"value": "h3", "label": "H3"},
        {"value": "h4", "label": "H4"},
        {"value": "h5", "label": "H5"},
        {"value": "h6", "label": "H6"}
      ]
    }
  ]
}
{% endschema %}
