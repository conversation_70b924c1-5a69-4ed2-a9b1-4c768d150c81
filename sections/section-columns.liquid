<!-- /sections/section-columns.liquid -->

{%- render 'multicolumn', type: 'images' -%}

{% schema %}
{
  "name": "Text columns with images",
  "class": "index-section",
  "settings": [
    {
      "type": "header",
      "content": "Typography"
    },
    {
      "type": "text",
      "id": "title",
      "label": "Heading"
    },
    {
      "type": "select",
      "id": "heading_font_size",
      "label": "Heading size",
      "default": "heading-medium",
      "options": [
        {"value": "heading-mini", "label": "Mini"},
        {"value": "heading-x-small", "label": "Extra small"},
        {"value": "heading-small", "label": "Small"},
        {"value": "heading-medium", "label": "Medium"},
        {"value": "heading-large", "label": "Large"},
        {"value": "heading-x-large", "label": "Extra large"}
      ]
    },
    {
      "type": "select",
      "id": "align_heading",
      "label": "Heading",
      "default": "text-left",
      "options": [
        {
          "value": "text-left",
          "label": "Left"
        },
        {
          "value": "text-center",
          "label": "Centered"
        }
      ]
    },
    {
      "type": "select",
      "id": "align_text",
      "label": "Text",
      "default": "text-left",
      "options": [
        {
          "value": "text-left",
          "label": "Left"
        },
        {
          "value": "text-center",
          "label": "Centered"
        }
      ]
    },
    {
      "type": "header",
      "content": "Layout"
    },
    {
      "type": "select",
      "id": "width",
      "label": "Width",
      "default": "wrapper--full-padded",
      "options": [
        {"value": "wrapper--full-padded", "label": "Full width padded"},
        {"value": "wrapper", "label": "Normal"}
      ]
    },
    {
      "type": "select",
      "id": "align_columns",
      "label": "Columns",
      "default": "flex-align-top",
      "options": [
        {
          "value": "flex-align-top",
          "label": "Top"
        },
        {
          "value": "flex-align-center",
          "label": "Centered"
        }
      ]
    },
    {
      "type": "header",
      "content": "Desktop"
    },
    {
      "type": "select",
      "id": "layout",
      "label": "Layout",
      "options": [
        {
          "value": "grid",
          "label": "Grid"
        },
        {
          "value": "slider",
          "label": "Slider"
        }
      ],
      "default": "grid"
    },
    {
      "type": "range",
      "id": "grid",
      "label": "Items per row",
      "info": "Applies only when layout is set to Grid",
      "min": 2,
      "max": 6,
      "step": 1,
      "default": 3
    },
    {
      "type": "header",
      "content": "Mobile"
    },
    {
      "type": "select",
      "id": "layout_mobile",
      "label": "Layout",
      "options": [
        {
          "value": "1",
          "label": "1 item per row"
        },
        {
          "value": "2",
          "label": "2 items per row"
        },
        {
          "value": "slider",
          "label": "Slider"
        }
      ],
      "default": "slider"
    },
    {
      "type": "header",
      "content": "Colors"
    },
    {
      "type": "color_scheme",
      "id": "color_scheme",
      "default": "scheme_1",
      "label": "Color scheme"
    },
    {
      "type": "header",
      "content": "Padding"
    },
    {
      "type": "range",
      "id": "padding_top",
      "min": 0,
      "max": 100,
      "step": 1,
      "unit": "px",
      "label": "Top",
      "default": 50
    },
    {
      "type": "range",
      "id": "padding_bottom",
      "min": 0,
      "max": 100,
      "step": 1,
      "unit": "px",
      "label": "Bottom",
      "default": 50
    },
    {
      "type": "header",
      "content": "Advanced"
    },
    {
      "type": "select",
      "id": "heading_tag",
      "label": "Heading SEO tag",
      "default": "automatic",
      "options": [
        {"value": "automatic", "label": "Automatic"},
        {"value": "h1", "label": "H1"},
        {"value": "h2", "label": "H2"},
        {"value": "h3", "label": "H3"},
        {"value": "h4", "label": "H4"},
        {"value": "h5", "label": "H5"},
        {"value": "h6", "label": "H6"}
      ]
    }
  ],
  "blocks": [
    {
      "type": "image",
      "name": "Image",
      "settings": [
        {
          "type": "header",
          "content": "Content"
        },
        {
          "type": "image_picker",
          "id": "image",
          "label": "Image",
          "info": "1400 x 1400px .jpg recommended. [Learn more](https://invisiblethemes.com/link/broadcast/images)"
        },
        {
          "type": "select",
          "id": "image_shape",
          "label": "Image shape",
          "options": [
            {
              "value": "normal",
              "label": "Normal"
            },
            {
              "value": "circle",
              "label": "Circle"
            },
            {
              "value": "rounded",
              "label": "Rounded"
            },
            {
              "value": "blob-one",
              "label": "Blob one"
            },
            {
              "value": "blob-two",
              "label": "Blob two"
            },
            {
              "value": "blob-three",
              "label": "Blob three"
            }
          ],
          "default": "normal",
          "info": "1:1 aspect ratio recommended"
        },
        {
          "type": "header",
          "content": "Typography"
        },
        {
          "type": "text",
          "id": "title",
          "label": "Heading",
          "default": "Add a title or tagline"
        },
        {
          "type": "select",
          "id": "heading_font_size",
          "label": "Heading size",
          "default": "heading-small",
          "options": [
            {"value": "heading-mini", "label": "Mini"},
            {"value": "heading-x-small", "label": "Extra small"},
            {"value": "heading-small", "label": "Small"},
            {"value": "heading-medium", "label": "Medium"},
            {"value": "heading-large", "label": "Large"},
            {"value": "heading-x-large", "label": "Extra large"}
          ]
        },
        {
          "type": "richtext",
          "id": "text",
          "label": "Text",
          "default": "<p>Use this text to describe products, share details on availability and style, or as a space to display recent reviews or FAQs.</p>"
        },
        {
          "type": "select",
          "id": "text_font_size",
          "label": "Text size",
          "info": "Automatically generated by the base size. Applies only when content in Text field is set to Paragraph.",
          "default": "body-medium",
          "options": [
            {"value": "body-x-small", "label": "Extra small"},
            {"value": "body-small", "label": "Small"},
            {"value": "body-medium", "label": "Medium"},
            {"value": "body-large", "label": "Large"},
            {"value": "body-x-large", "label": "Extra large"}
          ]
        },
        {
          "type": "header",
          "content": "Button"
        },
        {
          "type": "text",
          "id": "button_text",
          "label": "Text",
          "default": "Learn more",
          "info": "Leave blank to link entire image"
        },
        {
          "type": "url",
          "id": "button_url",
          "label": "Link"
        },
        {
          "type": "select",
          "id": "button_type",
          "label": "Color",
          "default": "btn--primary",
          "options": [
            {"value": "btn--black", "label": "Black"},
            {"value": "btn--white", "label": "White"},
            {"value": "btn--primary", "label": "Primary"},
            {"value": "btn--secondary", "label": "Secondary"}
          ]
        },
        {
          "type": "select",
          "id": "button_size",
          "label": "Size",
          "default": "",
          "options": [
            {"label": "Small", "value": "btn--small"},
            {"label": "Medium", "value": ""},
            {"label": "Large", "value": "btn--large"}
          ]
        },
        {
          "type": "select",
          "id": "button_style",
          "label": "Style",
          "default": "btn--solid",
          "options": [
            {"label": "Solid", "value": "btn--solid"},
            {"label": "Outline", "value": "btn--outline"},
            {"label": "Text", "value": "btn--text"}
          ]
        },
        {
          "type": "checkbox",
          "id": "show_arrow",
          "label": "Show arrow",
          "default": false
        },
        {
          "type": "header",
          "content": "Desktop"
        },
        {
          "type": "range",
          "id": "image_aspect_ratio",
          "min": 0.5,
          "max": 1.5,
          "step": 0.1,
          "unit": ":1",
          "label": "Image aspect ratio",
          "info": "Wide to tall",
          "default": 1
        },
        {
          "type": "header",
          "content": "Mobile"
        },
        {
          "type": "range",
          "id": "mobile_image_aspect_ratio",
          "min": 0.5,
          "max": 1.5,
          "step": 0.1,
          "unit": ":1",
          "label": "Image aspect ratio",
          "info": "Wide to tall",
          "default": 1
        },
        {
          "type": "header",
          "content": "Advanced"
        },
        {
          "type": "select",
          "id": "heading_tag",
          "label": "Heading SEO tag",
          "default": "automatic",
          "options": [
            {"value": "automatic", "label": "Automatic"},
            {"value": "h1", "label": "H1"},
            {"value": "h2", "label": "H2"},
            {"value": "h3", "label": "H3"},
            {"value": "h4", "label": "H4"},
            {"value": "h5", "label": "H5"},
            {"value": "h6", "label": "H6"}
          ]
        }
      ]
    }
  ],
  "presets": [
    {
      "name": "Text columns with images",
      "blocks": [
        {
          "type": "image"
        },
        {
          "type": "image"
        },
        {
          "type": "image"
        }
      ]
    }
  ],
  "disabled_on": {
    "groups": ["header", "aside", "footer"]
  }
}
{% endschema %}
