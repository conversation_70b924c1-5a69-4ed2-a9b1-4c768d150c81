/*
 * ------------------------------------------------------------
 * IMPORTANT: The contents of this file are auto-generated.
 *
 * This file may be updated by the Shopify admin theme editor
 * or related systems. Please exercise caution as any changes
 * made to this file may be overwritten.
 * ------------------------------------------------------------
 */
{
  "type": "footer",
  "name": "Footer",
  "sections": {
    "footer": {
      "type": "footer",
      "blocks": {
        "footer-2": {
          "type": "text",
          "settings": {
            "image_width": 150,
            "title": "Our Vision",
            "heading_font_size": "heading-mini",
            "text": "<p>At Fancy's Hair Innovation, our vision is to empower individuals to embrace their natural beauty with confidence. We strive to create high-quality, effective hair care solutions that nourish, protect, and enhance every strand.</p>",
            "column_width": 33,
            "enable_accordion": false
          }
        },
        "footer-1": {
          "type": "linklist",
          "settings": {
            "linklist": "footer",
            "title": "Customer Care",
            "heading_font_size": "heading-mini",
            "column_width": 33,
            "enable_accordion": false
          }
        },
        "footer-0": {
          "type": "newsletter",
          "settings": {
            "title": "Newsletter",
            "heading_font_size": "heading-mini",
            "text": "<p>Subscribe to get special offers, free giveaways, and once-in-a-lifetime deals.</p>",
            "show_name": true,
            "button_text": "Join",
            "show_arrow": false,
            "terms": true,
            "button_type": "btn--secondary",
            "button_size": "",
            "button_style": "btn--solid",
            "show_social_links": false,
            "column_width": 33
          }
        }
      },
      "block_order": [
        "footer-2",
        "footer-1",
        "footer-0"
      ],
      "settings": {
        "logo_image_width": 200,
        "color_scheme": "scheme-8",
        "border_color": "#212121",
        "padding_top": 30,
        "padding_bottom": 30
      }
    },
    "supporting-menu": {
      "type": "section-supporting-menu",
      "custom_css": [],
      "settings": {
        "linklist": "",
        "text_font_size": "body-small",
        "footer_payment_enable": true,
        "show_locale_selector": false,
        "show_globe_icon": false,
        "show_country_selector": false,
        "show_country_name": false,
        "show_country_flag": true,
        "color_scheme": "scheme-8",
        "padding_top": 30,
        "padding_bottom": 30
      }
    }
  },
  "order": [
    "footer",
    "supporting-menu"
  ]
}
