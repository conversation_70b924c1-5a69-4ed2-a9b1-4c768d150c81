/*
 * ------------------------------------------------------------
 * IMPORTANT: The contents of this file are auto-generated.
 *
 * This file may be updated by the Shopify admin theme editor
 * or related systems. Please exercise caution as any changes
 * made to this file may be overwritten.
 * ------------------------------------------------------------
 */
{
  "sections": {
    "main": {
      "type": "main-page",
      "disabled": true,
      "settings": {
        "subheading": "",
        "show_title": true,
        "heading_font_size": "heading-large",
        "show_content": true,
        "text_font_size": "body-medium",
        "align_text": "text-left",
        "width": "wrapper",
        "padding_top": 50,
        "padding_bottom": 50
      }
    },
    "double-1": {
      "type": "section-double",
      "blocks": {
        "heading-1-1": {
          "type": "heading",
          "settings": {
            "heading_font_size": "heading-x-large"
          }
        },
        "text-1-1": {
          "type": "text",
          "settings": {
            "text_font_size": "body-medium"
          }
        }
      },
      "block_order": [
        "heading-1-1",
        "text-1-1"
      ],
      "settings": {
        "image_width": "one-half",
        "text_alignment": "text-left",
        "layout": "is-reversed",
        "width": "wrapper--full",
        "height": "sixty-fifty-height-hero",
        "mobile_height": "four-fifty-height-hero--mobile",
        "padding_top": 0,
        "padding_bottom": 50
      }
    },
    "columns": {
      "type": "section-columns",
      "blocks": {
        "image-1": {
          "type": "image",
          "settings": {
            "button_text": "",
            "button_url": "",
            "button_type": "btn--primary",
            "button_style": "btn--solid",
            "image_aspect_ratio": 1.2
          }
        },
        "image-2": {
          "type": "image",
          "settings": {
            "button_text": "",
            "button_url": "",
            "button_type": "btn--primary",
            "button_style": "btn--solid",
            "image_aspect_ratio": 1.2
          }
        },
        "image-3": {
          "type": "image",
          "settings": {
            "button_text": "",
            "button_url": "",
            "button_type": "btn--primary",
            "button_style": "btn--solid",
            "image_aspect_ratio": 1.2
          }
        }
      },
      "block_order": [
        "image-1",
        "image-2",
        "image-3"
      ],
      "settings": {
        "align_text": "text-center",
        "width": "wrapper--full-padded",
        "align_columns": "flex-align-top",
        "layout_mobile": "slider",
        "padding_top": 50,
        "padding_bottom": 50
      }
    },
    "double-2": {
      "type": "section-double",
      "blocks": {
        "heading-2-1": {
          "type": "heading",
          "settings": {
            "heading_font_size": "heading-x-large"
          }
        },
        "text-2-1": {
          "type": "text",
          "settings": {
            "text_font_size": "body-medium"
          }
        }
      },
      "block_order": [
        "heading-2-1",
        "text-2-1"
      ],
      "settings": {
        "image_width": "one-half",
        "text_alignment": "text-left",
        "width": "wrapper--full",
        "height": "sixty-fifty-height-hero",
        "mobile_height": "four-fifty-height-hero--mobile",
        "padding_top": 50,
        "padding_bottom": 50
      }
    },
    "announcement": {
      "type": "section-announcement",
      "blocks": {
        "text": {
          "type": "text",
          "settings": {
            "target_device_enabled": false,
            "target_device": "mobile"
          }
        }
      },
      "block_order": [
        "text"
      ],
      "settings": {
        "slider_speed": 7,
        "marquee_speed": 100,
        "layout": "marquee",
        "padding_top": 0,
        "padding_bottom": 0
      }
    },
    "collections-list": {
      "type": "section-collections-list",
      "blocks": {
        "collection-1": {
          "type": "collection",
          "settings": {}
        },
        "collection-2": {
          "type": "collection",
          "settings": {}
        },
        "collection-3": {
          "type": "collection",
          "settings": {}
        },
        "collection-4": {
          "type": "collection",
          "settings": {}
        }
      },
      "block_order": [
        "collection-1",
        "collection-2",
        "collection-3",
        "collection-4"
      ],
      "settings": {
        "title": "",
        "aspect_ratio": 1.2,
        "layout": "grid",
        "padding_top": 50,
        "padding_bottom": 32
      }
    },
    "press-logos": {
      "type": "section-press-logos",
      "blocks": {
        "logo-item-1": {
          "type": "logo-item-header",
          "settings": {
            "logo_width": 150,
            "link": "",
            "heading_font_size": "heading-small"
          }
        },
        "logo-item-2": {
          "type": "logo-item-header",
          "settings": {
            "logo_width": 150,
            "link": "",
            "heading_font_size": "heading-small"
          }
        },
        "logo-item-3": {
          "type": "logo-item-header",
          "settings": {
            "logo_width": 150,
            "link": "",
            "heading_font_size": "heading-small"
          }
        },
        "logo-item-4": {
          "type": "logo-item-header",
          "settings": {
            "logo_width": 150,
            "link": "",
            "heading_font_size": "heading-small"
          }
        }
      },
      "block_order": [
        "logo-item-1",
        "logo-item-2",
        "logo-item-3",
        "logo-item-4"
      ],
      "settings": {
        "enable_heading_slider": false,
        "logo_opacity": 60,
        "padding_top": 50,
        "padding_bottom": 50
      }
    },
    "divider": {
      "type": "section-divider",
      "settings": {
        "show_line": true,
        "width": "wrapper--full-padded",
        "padding_top": 31,
        "padding_bottom": 50
      }
    }
  },
  "order": [
    "main",
    "double-1",
    "columns",
    "double-2",
    "announcement",
    "collections-list",
    "press-logos",
    "divider"
  ]
}
