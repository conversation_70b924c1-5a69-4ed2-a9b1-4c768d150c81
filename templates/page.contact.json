/*
 * ------------------------------------------------------------
 * IMPORTANT: The contents of this file are auto-generated.
 *
 * This file may be updated by the Shopify admin theme editor
 * or related systems. Please exercise caution as any changes
 * made to this file may be overwritten.
 * ------------------------------------------------------------
 */
{
  "sections": {
    "main": {
      "type": "main-page",
      "settings": {
        "align_text": "text-center"
      }
    },
    "contact": {
      "type": "contact-form",
      "blocks": {
        "Name": {
          "type": "text",
          "settings": {}
        },
        "Email": {
          "type": "email",
          "settings": {}
        },
        "Message": {
          "type": "body",
          "settings": {}
        },
        "Optional-text": {
          "type": "formHeading",
          "settings": {
            "title": "Optional"
          }
        },
        "Urgent-checkbox": {
          "type": "checkbox",
          "settings": {
            "title": "This is urgent"
          }
        },
        "Options-select": {
          "type": "select",
          "settings": {
            "title": "Select an option",
            "label-one": "- Pick an option -",
            "label-two": "Option one",
            "label-three": "Option two",
            "label-four": "Option three",
            "label-five": "Option four",
            "label-six": "Option five"
          }
        }
      },
      "block_order": [
        "Name",
        "Email",
        "Message",
        "Optional-text",
        "Urgent-checkbox",
        "Options-select"
      ],
      "settings": {
        "title": "",
        "padding_top": 50,
        "padding_bottom": 50
      }
    },
    "faq": {
      "type": "section-accordion-group",
      "blocks": {
        "heading-0": {
          "type": "heading",
          "settings": {
            "title": "FAQ"
          }
        },
        "question-1": {
          "type": "question",
          "settings": {}
        },
        "question-2": {
          "type": "question",
          "settings": {}
        },
        "question-3": {
          "type": "question",
          "settings": {}
        }
      },
      "block_order": [
        "heading-0",
        "question-1",
        "question-2",
        "question-3"
      ],
      "disabled": true,
      "settings": {
        "width": "wrapper--narrow",
        "padding_top": 50,
        "padding_bottom": 50
      }
    }
  },
  "order": [
    "main",
    "contact",
    "faq"
  ]
}
