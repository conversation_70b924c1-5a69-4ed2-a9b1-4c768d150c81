/*
 * ------------------------------------------------------------
 * IMPORTANT: The contents of this file are auto-generated.
 *
 * This file may be updated by the Shopify admin theme editor
 * or related systems. Please exercise caution as any changes
 * made to this file may be overwritten.
 * ------------------------------------------------------------
 */
{
  "sections": {
    "main": {
      "type": "product",
      "blocks": {
        "title": {
          "type": "title",
          "settings": {
            "subheading_option": "breadcrumb",
            "heading_font_size": "heading-small",
            "padding_bottom": 8
          }
        },
        "divider_TpWiTc": {
          "type": "divider",
          "settings": {
            "show_line": false,
            "padding_bottom": 20
          }
        },
        "price": {
          "type": "price",
          "settings": {
            "text_font_size": "body-x-large",
            "padding_bottom": 16
          }
        },
        "code_C9HFD4": {
          "type": "code",
          "disabled": true,
          "settings": {
            "code": "<p style=\"font-size:14px; margin:0; padding:0; color:black;\">\n    <span style=\"height:8px; width:8px; background-color:#dc143c; border-radius:50%; display:inline-block; animation:blinking 1.5s infinite;\"></span>\n    <span style=\"color:#dc143c;\">Hurry Up!</span> Six <span style=\"font-weight:bold;\">people</span> are viewing this product.\n</p>\n<style>\n@keyframes blinking {\n    0% { opacity: 1.0; }\n    50% { opacity: 0.0; }\n    100% { opacity: 1.0; }\n}\n</style>",
            "padding_bottom": 16
          }
        },
        "upsell_UVL8px": {
          "type": "upsell",
          "settings": {
            "upsell_product": "the-natural-hair-regimen",
            "upsell_product_list": [
              "hydrating-gentle-cleansing-shampoo",
              "moisture-rich-strengthening-conditioner",
              "strength-repair-protein-treatment"
            ],
            "show_available_upsell_only": false,
            "layout": "slider",
            "autoplay": true,
            "autoplay_speed": 4,
            "bg_color": "",
            "padding_bottom": 16
          }
        },
        "variants": {
          "type": "variants",
          "settings": {
            "info_page": "",
            "size_chart_style": "text",
            "subscriptions_enable_selectors": false,
            "padding_bottom": 16
          }
        },
        "buttons": {
          "type": "buttons",
          "settings": {
            "show_quantity": true,
            "button_type": "btn--primary",
            "button_style": "btn--solid",
            "show_payment_button": true,
            "button_type_dynamic": "btn--secondary",
            "button_style_dynamic": "btn--solid",
            "show_gift_card_recipient": false,
            "padding_bottom": 32
          }
        },
        "pickup": {
          "type": "pickup",
          "disabled": true,
          "settings": {
            "padding_bottom": 32
          }
        },
        "tabs": {
          "type": "tab_richtext",
          "settings": {
            "show_description": true,
            "show_read_more": false,
            "title_1": "",
            "raw_content_1": "",
            "title_2": "",
            "raw_content_2": "",
            "title_3": "",
            "raw_content_3": "",
            "title_4": "",
            "raw_content_4": "",
            "title_5": "",
            "raw_content_5": "",
            "padding_bottom": 0
          }
        },
        "divider_1": {
          "type": "divider",
          "settings": {
            "show_line": true,
            "padding_bottom": 16
          }
        },
        "complementary-products": {
          "type": "complementary-products",
          "disabled": true,
          "settings": {
            "complementary_limit": 3,
            "layout": "slider",
            "autoplay": true,
            "autoplay_speed": 8,
            "bg_color": "",
            "padding_bottom": 16
          }
        },
        "icon_1": {
          "type": "icon",
          "disabled": true,
          "settings": {
            "icon_name": "icon-email",
            "icon_size": 20,
            "icon_color": "",
            "text": "<p>Free 30 Days Returns</p>",
            "text_font_size": "body-medium",
            "width": "full",
            "padding_bottom": 16
          }
        },
        "icon_2": {
          "type": "icon",
          "settings": {
            "icon_name": "icon-truck",
            "icon_size": 20,
            "icon_color": "",
            "text": "<p>Free shipping on orders over $50</p>",
            "text_font_size": "body-medium",
            "width": "full",
            "padding_bottom": 16
          }
        }
      },
      "block_order": [
        "title",
        "divider_TpWiTc",
        "price",
        "code_C9HFD4",
        "upsell_UVL8px",
        "variants",
        "buttons",
        "pickup",
        "tabs",
        "divider_1",
        "complementary-products",
        "icon_1",
        "icon_2"
      ],
      "settings": {
        "product_sticky_enable": true,
        "variant_image_scroll": true,
        "show_cart_bar": true,
        "enable_zoom": true,
        "enable_video_looping": false,
        "image_layout": "stacked",
        "image_size": "normal",
        "mobile_image_style": "thumbs",
        "color_scheme": "",
        "padding_top": 90,
        "padding_bottom": 30
      }
    },
    "1745229016e8536f24": {
      "type": "apps",
      "settings": {
        "include_margins": false
      }
    },
    "faq": {
      "type": "section-accordion",
      "blocks": {
        "faq-1": {
          "type": "text",
          "settings": {
            "title": "Frequently asked question",
            "heading_font_size": "heading-small",
            "text": "<p>Use this to answer some common questions you hear from your customers. You could discuss product details, size fit, shipping policies, or anything you think would help merchants make an informed decision about your products. This section will appear across all products.</p>"
          }
        },
        "faq-2": {
          "type": "text",
          "settings": {
            "title": "Frequently asked question",
            "heading_font_size": "heading-small",
            "text": "<p>Use this to answer some common questions you hear from your customers. You could discuss product details, size fit, shipping policies, or anything you think would help merchants make an informed decision about your products. This section will appear across all products.</p>"
          }
        },
        "faq-3": {
          "type": "text",
          "settings": {
            "title": "Frequently asked question",
            "heading_font_size": "heading-small",
            "text": "<p>Use this to answer some common questions you hear from your customers. You could discuss product details, size fit, shipping policies, or anything you think would help merchants make an informed decision about your products. This section will appear across all products.</p>"
          }
        },
        "faq-4": {
          "type": "text",
          "settings": {
            "title": "Frequently asked question",
            "heading_font_size": "heading-small",
            "text": "<p>Use this to answer some common questions you hear from your customers. You could discuss product details, size fit, shipping policies, or anything you think would help merchants make an informed decision about your products. This section will appear across all products.</p>"
          }
        }
      },
      "block_order": [
        "faq-1",
        "faq-2",
        "faq-3",
        "faq-4"
      ],
      "disabled": true,
      "settings": {
        "default_open": false,
        "title": "FAQ",
        "heading_font_size": "heading-small",
        "show_icon": false,
        "icon_name": "icon-award",
        "icon_size": 20,
        "width": "wrapper",
        "color_scheme": "",
        "icon_color": "",
        "padding_top": 30,
        "padding_bottom": 0
      }
    },
    "related": {
      "type": "related",
      "blocks": {
        "related": {
          "type": "related",
          "settings": {
            "product_recommendations_heading": "Related products",
            "limit": 4
          }
        },
        "recent": {
          "type": "recent",
          "settings": {
            "product_recently_heading": "Recently viewed",
            "product_recently_limit": 4
          }
        }
      },
      "block_order": [
        "related",
        "recent"
      ],
      "settings": {
        "enable_tabs": true,
        "heading_alignment": "text-center",
        "heading_font_size": "heading-small",
        "layout_mobile": "slider",
        "color_scheme": "",
        "padding_top": 30,
        "padding_bottom": 0
      }
    },
    "174891914828c434fb": {
      "type": "apps",
      "blocks": {
        "sendvio_email_sms_product_reviews_VwCgfU": {
          "type": "shopify://apps/sendvio-email-sms/blocks/product_reviews/6fb7816d-63ad-49f9-ae13-e7c831b17dc6",
          "settings": {
            "date_format": "hide",
            "show_write_review": true,
            "reviews_number_format": "number",
            "text_font_scale": 120,
            "background_color": "#ffffff",
            "texts_color": "#000000",
            "buttons_background": "#ffffff",
            "outline_button": "#cecece",
            "padding_top": 10,
            "padding_bottom": 20,
            "padding_horizontal": 20
          }
        }
      },
      "block_order": [
        "sendvio_email_sms_product_reviews_VwCgfU"
      ],
      "settings": {
        "include_margins": false
      }
    }
  },
  "order": [
    "main",
    "1745229016e8536f24",
    "faq",
    "related",
    "174891914828c434fb"
  ]
}
