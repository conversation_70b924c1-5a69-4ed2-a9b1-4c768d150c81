<p
  class="hero__description {{ block.settings.text_font_size }}"
  data-aos="hero"
  data-aos-order="auto"
  data-aos-anchor="#{{ section.id }}"
  {{ block.shopify_attributes }}
>
  {{ block.settings.text }}
</p>

{% schema %}
{
  "name": "Text",
  "tag": null,
  "settings": [
    {
      "type": "textarea",
      "id": "text",
      "label": "Text",
      "default": "Tell your brand's story through images."
    },
    {
      "type": "select",
      "id": "text_font_size",
      "label": "Text size",
      "info": "Automatically generated by the base size.",
      "default": "body-large",
      "options": [
        {"value": "body-x-small", "label": "Extra small"},
        {"value": "body-small", "label": "Small"},
        {"value": "body-medium", "label": "Medium"},
        {"value": "body-large", "label": "Large"},
        {"value": "body-x-large", "label": "Extra large"}
      ]
    }
  ],
  "presets": [
    {
      "name": "Text",
      "settings": {
        "text": "Tell your brand's story through images."
      }
    }
  ]
}
{% endschema %}
