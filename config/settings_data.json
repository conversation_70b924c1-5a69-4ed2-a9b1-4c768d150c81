/*
 * ------------------------------------------------------------
 * IMPORTANT: The contents of this file are auto-generated.
 *
 * This file may be updated by the Shopify admin theme editor
 * or related systems. Please exercise caution as any changes
 * made to this file may be overwritten.
 * ------------------------------------------------------------
 */
{
  "current": {
    "menu_bg_color": "#faf3e8",
    "header_link": "#000000",
    "submenu_bg_color": "#faf3e8",
    "submenu_link_color": "#000000",
    "menu_transparent_color": "#faf3e8",
    "sale_color": "#c64113",
    "type_nav_font": "figtree_n5",
    "nav_font_size": 14,
    "nav_letter_spacing": 25,
    "type_nav_caps": true,
    "type_base_font": "figtree_n4",
    "base_font_size": 16,
    "type_subheading_font": "figtree_n4",
    "sub_letter_spacing": 25,
    "type_sub_uppercase": true,
    "type_header_font": "besley_n7",
    "heading_mini": 20,
    "heading_x_small": 26,
    "heading_small": 40,
    "heading_medium": 60,
    "heading_large": 78,
    "heading_x_large": 96,
    "heading_mini_mobile": 20,
    "heading_x_small_mobile": 26,
    "heading_small_mobile": 32,
    "heading_medium_mobile": 40,
    "heading_large_mobile": 52,
    "heading_x_large_mobile": 55,
    "type_btn_font": "figtree_n4",
    "btn_border_style": "pill",
    "btn_size": 12,
    "dots_style": "circle",
    "icon_style": "1.5",
    "parallax_enable": true,
    "parallax_strength": 15,
    "product_grid_outline": false,
    "enable_superscript": true,
    "show_scroll_top_button": true,
    "instagram_link": "https://instagram.com/shopify",
    "facebook_link": "https://facebook.com/shopify",
    "tiktok_link": "https://tiktok.com/@shopify",
    "pinterest_link": "https://pinterest.com/shopify",
    "product_grid_aspect_ratio": 1.3,
    "sale_bg_color": "#c64113",
    "sale_text_color": "#ffffff",
    "badge_bg_color": "#ffffff",
    "badge_text_color": "#212121",
    "show_sold_out_badge": true,
    "show_automatic_new_badge": false,
    "badge_new_date_limit": 5,
    "form_style": "modern",
    "show_variant_image": false,
    "final_sale": true,
    "swatch_size": "regular",
    "swatch_style": "circle",
    "sibling_style": "image",
    "collection_swatch_style": "text-slider",
    "swatch_color_list": "Black: #000000\nWhite: #fafafa\nBlank: blank.png\nBaby Blue: #D0D9E1\nBlush: #DBB9AE\nBurgundy: #4C1523\nCharcoal: #3A3838\nLilac: #DDCFD8\nMustard: #C08035\nRose: #9D4D4A\nTaupe: #AA8880\nTurquoise: #0389B9\nGreen: #698656\nGrey: #C6B8A8\nOlive: #758C7D\nRed: #D05645\nPurple: #BBB7CF\nYellow: #DDB400\nOrange: #E0834B\nBlue: #275E7E\nPink: #CD9485",
    "favicon": "shopify://shop_images/fancy4-removebg-preview_1.png",
    "free_shipping_limit": "50",
    "show_lock_icon": true,
    "show_recently_viewed_products": true,
    "header_link_hover": "#AB8C52",
    "show_logo_bg": false,
    "checkout_logo_position": "left",
    "checkout_logo_size": "large",
    "checkout_body_background_color": "#fff",
    "checkout_input_background_color_mode": "white",
    "checkout_sidebar_background_color": "#f9f9f9",
    "checkout_heading_font": "Open Sans",
    "checkout_body_font": "Open Sans",
    "checkout_accent_color": "#ab8c52",
    "checkout_button_color": "#ab8c52",
    "checkout_error_color": "#c85c42",
    "sale_background_color": "#be563f",
    "content_for_index": [],
    "blocks": {
      "4015264832934714879": {
        "type": "shopify://apps/ecomsend-popups/blocks/app/a5670cf8-5705-4b01-ad9e-447033feae26",
        "disabled": false,
        "settings": {}
      },
      "11532412952436166569": {
        "type": "shopify://apps/loox-reviews/blocks/loox-inject/5c3b337f-fd14-4df5-b1d6-80ec13e6e28e",
        "disabled": false,
        "settings": {}
      },
      "15683396631634586217": {
        "type": "shopify://apps/inbox/blocks/chat/841fc607-4181-4ad1-842d-e24d7f8bad6b",
        "disabled": true,
        "settings": {
          "button_color": "#dc954d",
          "secondary_color": "#FFFFFF",
          "ternary_color": "#6A6A6A",
          "button_icon": "chat_bubble",
          "button_text": "chat_with_us",
          "button_horizontal_position": "bottom_right",
          "button_vertical_position": "lowest",
          "greeting_message": ""
        }
      },
      "855628211100114053": {
        "type": "shopify://apps/klaviyo-email-marketing-sms/blocks/klaviyo-onsite-embed/2632fe16-c075-4321-a88b-50b567f42507",
        "disabled": false,
        "settings": {}
      }
    },
    "color_schemes": {
      "scheme-1": {
        "settings": {
          "section_text": "#000000",
          "section_bg": "#faf3e8",
          "section_bg_light": "#f4f2e2",
          "section_bg_gradient": "",
          "btn_primary_bg": "#dc954d",
          "btn_primary_text": "#ffffff",
          "btn_primary_border": "#dc954d",
          "btn_secondary_bg": "#12543f",
          "btn_secondary_text": "#ffffff",
          "btn_secondary_border": "#12543f",
          "accent": "#dc954d",
          "lines_and_border": "#faead3",
          "overlay": "#000000",
          "links": "#000000"
        }
      },
      "scheme-2": {
        "settings": {
          "section_text": "#ffffff",
          "section_bg": "#dc954d",
          "section_bg_light": "#f4f2e2",
          "section_bg_gradient": "",
          "btn_primary_bg": "#eed9c2",
          "btn_primary_text": "#000000",
          "btn_primary_border": "#eed9c2",
          "btn_secondary_bg": "#ecb935",
          "btn_secondary_text": "#000000",
          "btn_secondary_border": "#ecb935",
          "accent": "#ffffff",
          "lines_and_border": "#f3f3f3",
          "overlay": "#000000",
          "links": "#ffffff"
        }
      },
      "scheme-3": {
        "settings": {
          "section_text": "#000000",
          "section_bg": "#eed9c2",
          "section_bg_light": "#faf3e8",
          "section_bg_gradient": "",
          "btn_primary_bg": "#dc954d",
          "btn_primary_text": "#000000",
          "btn_primary_border": "#dc954d",
          "btn_secondary_bg": "#12543f",
          "btn_secondary_text": "#ffffff",
          "btn_secondary_border": "#12543f",
          "accent": "#ecb935",
          "lines_and_border": "#dc954d",
          "overlay": "#000000",
          "links": "#000000"
        }
      },
      "scheme-4": {
        "settings": {
          "section_text": "#000000",
          "section_bg": "#faf3e8",
          "section_bg_light": "#eed9c2",
          "section_bg_gradient": "",
          "btn_primary_bg": "#be979f",
          "btn_primary_text": "#000000",
          "btn_primary_border": "#be979f",
          "btn_secondary_bg": "#dc954d",
          "btn_secondary_text": "#000000",
          "btn_secondary_border": "#dc954d",
          "accent": "#f7ddba",
          "lines_and_border": "#dc954d",
          "overlay": "#000000",
          "links": "#12543f"
        }
      },
      "scheme-5": {
        "settings": {
          "section_text": "#faf3e8",
          "section_bg": "#4b2e11",
          "section_bg_light": "#ab8660",
          "section_bg_gradient": "",
          "btn_primary_bg": "#f7ddba",
          "btn_primary_text": "#000000",
          "btn_primary_border": "#f7ddba",
          "btn_secondary_bg": "#d1def9",
          "btn_secondary_text": "#000000",
          "btn_secondary_border": "#d1def9",
          "accent": "#dc954d",
          "lines_and_border": "#faf3e8",
          "overlay": "#000000",
          "links": "#faf3e8"
        }
      },
      "scheme-6": {
        "settings": {
          "section_text": "#212121",
          "section_bg": "#ab8660",
          "section_bg_light": "#faf3e8",
          "section_bg_gradient": "",
          "btn_primary_bg": "#dc954d",
          "btn_primary_text": "#000000",
          "btn_primary_border": "#dc954d",
          "btn_secondary_bg": "#d1def9",
          "btn_secondary_text": "#000000",
          "btn_secondary_border": "#d1def9",
          "accent": "#ab8c52",
          "lines_and_border": "#f3f3f3",
          "overlay": "#000000",
          "links": "#282c2e"
        }
      },
      "scheme-7": {
        "settings": {
          "section_text": "#000000",
          "section_bg": "#faf3e8",
          "section_bg_light": "#f4f2e2",
          "section_bg_gradient": "",
          "btn_primary_bg": "#de8d9b",
          "btn_primary_text": "#000000",
          "btn_primary_border": "#de8d9b",
          "btn_secondary_bg": "#dc954d",
          "btn_secondary_text": "#000000",
          "btn_secondary_border": "#dc954d",
          "accent": "#f7ddba",
          "lines_and_border": "#dc954d",
          "overlay": "#000000",
          "links": "#000000"
        }
      },
      "scheme-8": {
        "settings": {
          "section_text": "#000000",
          "section_bg": "#f7ddba",
          "section_bg_light": "#dc954d",
          "section_bg_gradient": "",
          "btn_primary_bg": "#be979f",
          "btn_primary_text": "#000000",
          "btn_primary_border": "#be979f",
          "btn_secondary_bg": "#4b2e11",
          "btn_secondary_text": "#ffffff",
          "btn_secondary_border": "#4b2e11",
          "accent": "#eed9c2",
          "lines_and_border": "#ab8660",
          "overlay": "#000000",
          "links": "#000000"
        }
      },
      "scheme-9": {
        "settings": {
          "section_text": "#ffffff",
          "section_bg": "#000000",
          "section_bg_light": "#212121",
          "section_bg_gradient": "",
          "btn_primary_bg": "#dc954d",
          "btn_primary_text": "#000000",
          "btn_primary_border": "#dc954d",
          "btn_secondary_bg": "#12543f",
          "btn_secondary_text": "#ffffff",
          "btn_secondary_border": "#12543f",
          "accent": "#dc954d",
          "lines_and_border": "#ffffff",
          "overlay": "#000000",
          "links": "#ffffff"
        }
      },
      "scheme-10": {
        "settings": {
          "section_text": "#ffffff",
          "section_bg": "#000000",
          "section_bg_light": "#212121",
          "section_bg_gradient": "",
          "btn_primary_bg": "#dc954d",
          "btn_primary_text": "#000000",
          "btn_primary_border": "#dc954d",
          "btn_secondary_bg": "#12543f",
          "btn_secondary_text": "#ffffff",
          "btn_secondary_border": "#12543f",
          "accent": "#dc954d",
          "lines_and_border": "#ffffff",
          "overlay": "#000000",
          "links": "#ffffff"
        }
      },
      "scheme-11": {
        "settings": {
          "section_text": "#ffffff",
          "section_bg": "#000000",
          "section_bg_light": "#212121",
          "section_bg_gradient": "",
          "btn_primary_bg": "#dc954d",
          "btn_primary_text": "#000000",
          "btn_primary_border": "#dc954d",
          "btn_secondary_bg": "#12543f",
          "btn_secondary_text": "#ffffff",
          "btn_secondary_border": "#12543f",
          "accent": "#dc954d",
          "lines_and_border": "#ffffff",
          "overlay": "#000000",
          "links": "#ffffff"
        }
      }
    }
  },
  "presets": {
    "Clean": {
      "color_scheme": "scheme-1",
      "menu_bg_color": "#fcfbf9",
      "header_link": "#212121",
      "submenu_bg_color": "#fcfbf9",
      "submenu_link_color": "#212121",
      "menu_transparent_color": "#fcfbf9",
      "sale_color": "#212121",
      "type_nav_font": "dm_sans_n4",
      "type_nav_caps": false,
      "type_base_font": "dm_sans_n4",
      "base_font_size": 16,
      "type_subheading_font": "karla_n4",
      "sub_letter_spacing": 0,
      "subheading_size_desktop": 12,
      "subheading_size_mobile": 14,
      "type_header_font": "bricolage_grotesque_n7",
      "heading_letter_spacing": 0,
      "type_header_uppercase": true,
      "heading_mini": 12,
      "heading_x_small": 16,
      "heading_small": 20,
      "heading_medium": 36,
      "heading_large": 48,
      "heading_x_large": 60,
      "heading_mini_mobile": 12,
      "heading_x_small_mobile": 16,
      "heading_small_mobile": 20,
      "heading_medium_mobile": 24,
      "heading_large_mobile": 32,
      "type_btn_font": "karla_n4",
      "btn_border_style": "square",
      "btn_size": 14,
      "btn_letter_spacing": 25,
      "product_grid_outline": false,
      "enable_superscript": true,
      "show_scroll_top_button": true,
      "instagram_link": "https://instagram.com/shopify",
      "tiktok_link": "https://tiktok.com/@shopify",
      "product_grid_center": true,
      "product_grid_aspect_ratio": 1.1,
      "sale_bg_color": "#e9d2c2",
      "sale_text_color": "#212121",
      "badge_bg_color": "#ffffff",
      "badge_text_color": "#212121",
      "show_sold_out_badge": true,
      "show_automatic_new_badge": true,
      "badge_new_date_limit": 20,
      "form_style": "modern",
      "show_variant_image": false,
      "final_sale": true,
      "swatch_size": "regular",
      "swatch_style": "circle",
      "sibling_style": "swatch",
      "collection_swatch_style": "limited",
      "swatch_color_list": "Black: #000000\nWhite: #fafafa\nBlank: blank.png\nBronze: #A07636\nSilver: #CECBC7",
      "free_shipping_limit": "200",
      "show_lock_icon": true,
      "header_link_hover": "#AB8C52",
      "show_logo_bg": false,
      "checkout_logo_position": "left",
      "checkout_logo_size": "large",
      "checkout_body_background_color": "#fff",
      "checkout_input_background_color_mode": "white",
      "checkout_sidebar_background_color": "#f9f9f9",
      "checkout_heading_font": "Open Sans",
      "checkout_body_font": "Open Sans",
      "checkout_accent_color": "#ab8c52",
      "checkout_button_color": "#ab8c52",
      "checkout_error_color": "#c85c42",
      "sale_background_color": "#be563f",
      "content_for_index": [],
      "color_schemes": {
        "scheme-1": {
          "settings": {
            "section_text": "#212121",
            "section_bg": "#fcfbf9",
            "section_bg_light": "#f5f2ec",
            "section_bg_gradient": "",
            "btn_primary_bg": "#212121",
            "btn_primary_text": "#ffffff",
            "btn_primary_border": "#212121",
            "btn_secondary_bg": "#ab8c52",
            "btn_secondary_text": "#ffffff",
            "btn_secondary_border": "#ab8c52",
            "accent": "#ab8c52",
            "lines_and_border": "#d1cdc4",
            "overlay": "#000000",
            "links": "#282c2e"
          }
        },
        "scheme-2": {
          "settings": {
            "section_text": "#ffffff",
            "section_bg": "#212121",
            "section_bg_light": "#f5f2ec",
            "section_bg_gradient": "",
            "btn_primary_bg": "#212121",
            "btn_primary_text": "#ffffff",
            "btn_primary_border": "#212121",
            "btn_secondary_bg": "#ffffff",
            "btn_secondary_text": "#000000",
            "btn_secondary_border": "#ab8c52",
            "accent": "#ab8c52",
            "lines_and_border": "#d1d0ce",
            "overlay": "#644d3e",
            "links": "#ffffff"
          }
        },
        "scheme-3": {
          "settings": {
            "section_text": "#212121",
            "section_bg": "#f5f2ec",
            "section_bg_light": "#ffffff",
            "section_bg_gradient": "",
            "btn_primary_bg": "#212121",
            "btn_primary_text": "#ffffff",
            "btn_primary_border": "#212121",
            "btn_secondary_bg": "#ab8c52",
            "btn_secondary_text": "#ffffff",
            "btn_secondary_border": "#ab8c52",
            "accent": "#ab8c52",
            "lines_and_border": "#d1cdc4",
            "overlay": "#000000",
            "links": "#282c2e"
          }
        },
        "scheme-4": {
          "settings": {
            "section_text": "#212121",
            "section_bg": "#b0a38b",
            "section_bg_light": "#f5f2ec",
            "section_bg_gradient": "",
            "btn_primary_bg": "#212121",
            "btn_primary_text": "#ffffff",
            "btn_primary_border": "#ffffff",
            "btn_secondary_bg": "#ab8c52",
            "btn_secondary_text": "#ffffff",
            "btn_secondary_border": "#ab8c52",
            "accent": "#ab8c52",
            "lines_and_border": "#f3f3f3",
            "overlay": "#000000",
            "links": "#282c2e"
          }
        },
        "scheme-5": {
          "settings": {
            "section_text": "#d8d2b3",
            "section_bg": "#868154",
            "section_bg_light": "#f5f2ec",
            "section_bg_gradient": "",
            "btn_primary_bg": "#212121",
            "btn_primary_text": "#ffffff",
            "btn_primary_border": "#212121",
            "btn_secondary_bg": "#ab8c52",
            "btn_secondary_text": "#ffffff",
            "btn_secondary_border": "#ab8c52",
            "accent": "#ab8c52",
            "lines_and_border": "#f3f3f3",
            "overlay": "#000000",
            "links": "#282c2e"
          }
        },
        "scheme-6": {
          "settings": {
            "section_text": "#212121",
            "section_bg": "#fcfbf9",
            "section_bg_light": "#f5f2ec",
            "section_bg_gradient": "",
            "btn_primary_bg": "#ffffff",
            "btn_primary_text": "#212121",
            "btn_primary_border": "#ffffff",
            "btn_secondary_bg": "#ab8c52",
            "btn_secondary_text": "#ffffff",
            "btn_secondary_border": "#ab8c52",
            "accent": "#ab8c52",
            "lines_and_border": "#d1d0ce",
            "overlay": "#000000",
            "links": "#282c2e"
          }
        },
        "scheme-7": {
          "settings": {
            "section_text": "#685a3f",
            "section_bg": "#f5f2ec",
            "section_bg_light": "#f5f2ec",
            "section_bg_gradient": "",
            "btn_primary_bg": "#212121",
            "btn_primary_text": "#ffffff",
            "btn_primary_border": "#212121",
            "btn_secondary_bg": "#ab8c52",
            "btn_secondary_text": "#ffffff",
            "btn_secondary_border": "#ab8c52",
            "accent": "#ab8c52",
            "lines_and_border": "#d1cdc4",
            "overlay": "#000000",
            "links": "#282c2e"
          }
        },
        "scheme-8": {
          "settings": {
            "section_text": "#212121",
            "section_bg": "#eae6dd",
            "section_bg_light": "#f5f2ec",
            "section_bg_gradient": "",
            "btn_primary_bg": "#212121",
            "btn_primary_text": "#ffffff",
            "btn_primary_border": "#212121",
            "btn_secondary_bg": "#ab8c52",
            "btn_secondary_text": "#ffffff",
            "btn_secondary_border": "#ab8c52",
            "accent": "#ab8c52",
            "lines_and_border": "#d1cdc4",
            "overlay": "#000000",
            "links": "#282c2e"
          }
        },
        "scheme-9": {
          "settings": {
            "section_text": "#212121",
            "section_bg": "#fcfbf9",
            "section_bg_light": "#f5f2ec",
            "section_bg_gradient": "",
            "btn_primary_bg": "#212121",
            "btn_primary_text": "#ffffff",
            "btn_primary_border": "#212121",
            "btn_secondary_bg": "#f5f2ec",
            "btn_secondary_text": "#212121",
            "btn_secondary_border": "#f5f2ec",
            "accent": "#ab8c52",
            "lines_and_border": "#eaeaea",
            "overlay": "#000000",
            "links": "#282c2e"
          }
        },
        "scheme-10": {
          "settings": {
            "section_text": "#ffffff",
            "section_bg": "rgba(0,0,0,0)",
            "section_bg_light": "#f5f2ec",
            "section_bg_gradient": "",
            "btn_primary_bg": "#212121",
            "btn_primary_text": "#ffffff",
            "btn_primary_border": "#ffffff",
            "btn_secondary_bg": "#ab8c52",
            "btn_secondary_text": "#ffffff",
            "btn_secondary_border": "#ab8c52",
            "accent": "#ab8c52",
            "lines_and_border": "#d1cdc4",
            "overlay": "#000000",
            "links": "#282c2e"
          }
        },
        "scheme-11": {
          "settings": {
            "section_text": "#ffffff",
            "section_bg": "rgba(0,0,0,0)",
            "section_bg_light": "#f5f2ec",
            "section_bg_gradient": "",
            "btn_primary_bg": "#212121",
            "btn_primary_text": "#ffffff",
            "btn_primary_border": "#ffffff",
            "btn_secondary_bg": "#ab8c52",
            "btn_secondary_text": "#ffffff",
            "btn_secondary_border": "#ab8c52",
            "accent": "#ab8c52",
            "lines_and_border": "#d1cdc4",
            "overlay": "#000000",
            "links": "#282c2e"
          }
        }
      }
    },
    "Modern": {
      "menu_bg_color": "#fcfcfc",
      "header_link": "#000000",
      "submenu_bg_color": "#fcfcfc",
      "submenu_link_color": "#000000",
      "menu_transparent_color": "#fcfcfc",
      "sale_color": "#d21212",
      "type_nav_font": "space_mono_n4",
      "nav_font_size": 15,
      "type_nav_caps": false,
      "type_base_font": "space_mono_n4",
      "base_font_size": 14,
      "body_letter_spacing": 0,
      "type_subheading_font": "oswald_n5",
      "sub_letter_spacing": 100,
      "type_sub_uppercase": true,
      "type_header_font": "oswald_n5",
      "type_header_uppercase": true,
      "heading_mini": 20,
      "heading_x_small": 30,
      "heading_small": 40,
      "heading_medium": 50,
      "heading_large": 64,
      "heading_x_large": 94,
      "heading_mini_mobile": 16,
      "heading_x_small_mobile": 22,
      "heading_small_mobile": 24,
      "heading_medium_mobile": 32,
      "heading_large_mobile": 40,
      "heading_x_large_mobile": 45,
      "type_btn_font": "space_mono_n4",
      "btn_border_style": "rounded",
      "btn_size": 12,
      "btn_letter_spacing": 100,
      "dots_style": "line",
      "show_image_gray_bg": "behind",
      "parallax_enable": false,
      "parallax_strength": 0,
      "product_grid_outline": false,
      "enable_superscript": true,
      "show_scroll_top_button": true,
      "instagram_link": "https://instagram.com/shopify",
      "tiktok_link": "https://tiktok.com/@shopify",
      "tumblr_link": "http://shopify.tumblr.com",
      "youtube_link": "",
      "product_grid_center": true,
      "product_grid_aspect_ratio": 1.2,
      "sale_bg_color": "#a54545",
      "sale_text_color": "#fcfcfc",
      "badge_bg_color": "rgba(0,0,0,0)",
      "badge_text_color": "#32393e",
      "show_sold_out_badge": true,
      "show_automatic_new_badge": true,
      "form_style": "modern",
      "show_labels": true,
      "variant_lines": true,
      "final_sale": true,
      "swatch_style": "circle",
      "sibling_style": "swatch",
      "swatch_color_list": "Black: #000000\nWhite: #fafafa\nBlue: #6686A8\nMacadamia: #F6F0E2\nBlank: blank.png\nCharcoal: #5D585A\nBrown: #806C61\nGrey: #EBE8E0\nSage: #A9C1B4\nMint: #96B0A7\nForest: #4B6867\nMango: #F7E7BB\nGreige: #9E998E\nTan: #D7B389\nSmoke Grey: #EDEAE2\nSun: #F8CD49\nPigment Dye Black: #444444\nAqua Blue: #4AAAC8\nCreme: #F7F2ED\nForest Green: #094C25\nHeather Grey: #BAB8B6\nHeather Sand: #EAE7DD\nIndigo Camo: #252F3F\nIndigo Paisley: #2D3953\nMulticolor: #6E1B1E\nOcean Blue: #4ACEEA\nOffwhite: #F7F7E8\nOlive Green: #928A35",
      "free_shipping_limit": "200",
      "show_lock_icon": true,
      "enable_accept_terms": false,
      "empty_cart_menu": "footer",
      "empty_cart_products": [],
      "text_color": "#030405",
      "bg_color": "#fcfcfc",
      "secondary_bg_color": "#ffffff",
      "accent_bright": "#1d2226",
      "link_color": "#020303",
      "site_border_color": "#f0f0f0",
      "header_bg": "#fcfcfc",
      "menu_link_color": "#000000",
      "footer_bg": "#000000",
      "footer_border": "#ffffff",
      "footer_text": "#ffffff",
      "footer_link": "#ffffff",
      "type_heading_size": 150,
      "type_heading_uppercase": true,
      "type_sub_font": "heading",
      "type_sub_size": 100,
      "type_body_size": 100,
      "type_nav_size": 90,
      "btn_font": "ibm_plex_sans_n4",
      "btn_primary_bg_color": "#020303",
      "btn_primary_text_color": "#ffffff",
      "btn_primary_border_color": "#020303",
      "btn_secondary_bg_color": "#79341b",
      "btn_secondary_text_color": "#ffffff",
      "btn_secondary_border_color": "#79341b",
      "checkout_logo_position": "left",
      "checkout_logo_size": "medium",
      "checkout_body_background_color": "#fff",
      "checkout_input_background_color_mode": "white",
      "checkout_sidebar_background_color": "#fafafa",
      "checkout_heading_font": "Open Sans",
      "checkout_body_font": "Open Sans",
      "checkout_accent_color": "#c5aa76",
      "checkout_button_color": "#c5aa76",
      "checkout_error_color": "#c85c42",
      "enable_estimate_shipping": true,
      "enable_additional_buttons": true,
      "show_view_cart_button": true,
      "show_free_shipping_message": true,
      "message": "You are ||amount|| away from free shipping.",
      "link_hover_color": "#bcb2a8",
      "header_link_hover": "#AB8C52",
      "show_logo_bg": false,
      "menu_link_hover_color": "#AB8C52",
      "submenu_link_hover_color": "#AB8C52",
      "footer_link_hover": "#212121",
      "enable_free_shipping_message": true,
      "sale_background_color": "#be563f",
      "sections": {
        "gift-card": {
          "type": "gift-card",
          "settings": {
            "logo_max_limit": 300
          }
        }
      },
      "content_for_index": [],
      "color_schemes": {
        "scheme-1": {
          "settings": {
            "section_text": "#030405",
            "section_bg": "#fcfcfc",
            "section_bg_light": "#ffffff",
            "section_bg_gradient": "",
            "btn_primary_bg": "#020303",
            "btn_primary_text": "#ffffff",
            "btn_primary_border": "#020303",
            "btn_secondary_bg": "#cdff00",
            "btn_secondary_text": "#000000",
            "btn_secondary_border": "#cdff00",
            "accent": "#1d2226",
            "lines_and_border": "#f0f0f0",
            "overlay": "#000000",
            "links": "#020303"
          }
        },
        "scheme-2": {
          "settings": {
            "section_text": "#ffffff",
            "section_bg": "#020303",
            "section_bg_light": "#1d2226",
            "section_bg_gradient": "",
            "btn_primary_bg": "#fcfcfc",
            "btn_primary_text": "#1d2226",
            "btn_primary_border": "#fcfcfc",
            "btn_secondary_bg": "#cdff00",
            "btn_secondary_text": "#000000",
            "btn_secondary_border": "#cdff00",
            "accent": "#cdff00",
            "lines_and_border": "#ffffff",
            "overlay": "#000000",
            "links": "#ffffff"
          }
        },
        "scheme-3": {
          "settings": {
            "section_text": "#000000",
            "section_bg": "#ffffff",
            "section_bg_light": "#fcfcfc",
            "section_bg_gradient": "",
            "btn_primary_bg": "#020303",
            "btn_primary_text": "#ffffff",
            "btn_primary_border": "#020303",
            "btn_secondary_bg": "#cdff00",
            "btn_secondary_text": "#020303",
            "btn_secondary_border": "#020303",
            "accent": "#f0f0f0",
            "lines_and_border": "#f0f0f0",
            "overlay": "#000000",
            "links": "#020303"
          }
        },
        "scheme-4": {
          "settings": {
            "section_text": "#000000",
            "section_bg": "#f0f0f0",
            "section_bg_light": "#fcfcfc",
            "section_bg_gradient": "",
            "btn_primary_bg": "#030405",
            "btn_primary_text": "#ffffff",
            "btn_primary_border": "#030405",
            "btn_secondary_bg": "#cdff00",
            "btn_secondary_text": "#030405",
            "btn_secondary_border": "#030405",
            "accent": "#1d2226",
            "lines_and_border": "#000000",
            "overlay": "#000000",
            "links": "#030405"
          }
        },
        "scheme-5": {
          "settings": {
            "section_text": "#000000",
            "section_bg": "#fcfcfc",
            "section_bg_light": "#ffffff",
            "section_bg_gradient": "",
            "btn_primary_bg": "#a54545",
            "btn_primary_text": "#fcfcfc",
            "btn_primary_border": "#a54545",
            "btn_secondary_bg": "#1d2226",
            "btn_secondary_text": "#ffffff",
            "btn_secondary_border": "#1d2226",
            "accent": "#f0f0f0",
            "lines_and_border": "#f0f0f0",
            "overlay": "#000000",
            "links": "#1d2226"
          }
        },
        "scheme-6": {
          "settings": {
            "section_text": "#000000",
            "section_bg": "#f2f2f2",
            "section_bg_light": "#f0f0f0",
            "section_bg_gradient": "",
            "btn_primary_bg": "#1d2226",
            "btn_primary_text": "#ffffff",
            "btn_primary_border": "#1d2226",
            "btn_secondary_bg": "#cdff00",
            "btn_secondary_text": "#000000",
            "btn_secondary_border": "#cdff00",
            "accent": "#ffffff",
            "lines_and_border": "#000000",
            "overlay": "#000000",
            "links": "#1d2226"
          }
        },
        "scheme-7": {
          "settings": {
            "section_text": "#000000",
            "section_bg": "#f9f9f9",
            "section_bg_light": "#fcfcfc",
            "section_bg_gradient": "",
            "btn_primary_bg": "#a54545",
            "btn_primary_text": "#ffffff",
            "btn_primary_border": "#a54545",
            "btn_secondary_bg": "#f0f0f0",
            "btn_secondary_text": "#000000",
            "btn_secondary_border": "#f0f0f0",
            "accent": "#f0f0f0",
            "lines_and_border": "#1d2226",
            "overlay": "#000000",
            "links": "#1d2226"
          }
        },
        "scheme-8": {
          "settings": {
            "section_text": "#000000",
            "section_bg": "#fcfcfc",
            "section_bg_light": "#f0f0f0",
            "section_bg_gradient": "",
            "btn_primary_bg": "#79341b",
            "btn_primary_text": "#ffffff",
            "btn_primary_border": "#79341b",
            "btn_secondary_bg": "#1d2226",
            "btn_secondary_text": "#ffffff",
            "btn_secondary_border": "#1d2226",
            "accent": "#79341b",
            "lines_and_border": "#f0f0f0",
            "overlay": "#000000",
            "links": "#1d2226"
          }
        },
        "scheme-9": {
          "settings": {
            "section_text": "#000000",
            "section_bg": "#fcfcfc",
            "section_bg_light": "#f0f0f0",
            "section_bg_gradient": "",
            "btn_primary_bg": "#79341b",
            "btn_primary_text": "#ffffff",
            "btn_primary_border": "#79341b",
            "btn_secondary_bg": "#1d2226",
            "btn_secondary_text": "#ffffff",
            "btn_secondary_border": "#1d2226",
            "accent": "#79341b",
            "lines_and_border": "#f0f0f0",
            "overlay": "#000000",
            "links": "#1d2226"
          }
        },
        "scheme-10": {
          "settings": {
            "section_text": "#000000",
            "section_bg": "#fcfcfc",
            "section_bg_light": "#f0f0f0",
            "section_bg_gradient": "",
            "btn_primary_bg": "#79341b",
            "btn_primary_text": "#ffffff",
            "btn_primary_border": "#79341b",
            "btn_secondary_bg": "#1d2226",
            "btn_secondary_text": "#ffffff",
            "btn_secondary_border": "#1d2226",
            "accent": "#79341b",
            "lines_and_border": "#f0f0f0",
            "overlay": "#000000",
            "links": "#1d2226"
          }
        },
        "scheme-11": {
          "settings": {
            "section_text": "#000000",
            "section_bg": "#fcfcfc",
            "section_bg_light": "#f0f0f0",
            "section_bg_gradient": "",
            "btn_primary_bg": "#79341b",
            "btn_primary_text": "#ffffff",
            "btn_primary_border": "#79341b",
            "btn_secondary_bg": "#1d2226",
            "btn_secondary_text": "#ffffff",
            "btn_secondary_border": "#1d2226",
            "accent": "#79341b",
            "lines_and_border": "#f0f0f0",
            "overlay": "#000000",
            "links": "#1d2226"
          }
        }
      }
    },
    "Bright": {
      "menu_bg_color": "#faf3e8",
      "header_link": "#000000",
      "submenu_bg_color": "#faf3e8",
      "submenu_link_color": "#000000",
      "menu_transparent_color": "#faf3e8",
      "sale_color": "#c64113",
      "type_nav_font": "figtree_n5",
      "nav_font_size": 14,
      "nav_letter_spacing": 25,
      "type_nav_caps": true,
      "type_base_font": "figtree_n4",
      "base_font_size": 16,
      "type_subheading_font": "figtree_n4",
      "sub_letter_spacing": 25,
      "type_sub_uppercase": true,
      "type_header_font": "besley_n7",
      "heading_mini": 20,
      "heading_x_small": 26,
      "heading_small": 40,
      "heading_medium": 60,
      "heading_large": 78,
      "heading_x_large": 96,
      "heading_mini_mobile": 20,
      "heading_x_small_mobile": 26,
      "heading_small_mobile": 32,
      "heading_medium_mobile": 40,
      "heading_large_mobile": 52,
      "heading_x_large_mobile": 55,
      "type_btn_font": "figtree_n4",
      "btn_border_style": "pill",
      "btn_size": 12,
      "dots_style": "circle",
      "icon_style": "1.5",
      "parallax_enable": true,
      "parallax_strength": 15,
      "product_grid_outline": false,
      "enable_superscript": true,
      "show_scroll_top_button": true,
      "instagram_link": "https://instagram.com/shopify",
      "facebook_link": "https://facebook.com/shopify",
      "tiktok_link": "https://tiktok.com/@shopify",
      "pinterest_link": "https://pinterest.com/shopify",
      "product_grid_aspect_ratio": 1.3,
      "sale_bg_color": "#c64113",
      "sale_text_color": "#ffffff",
      "badge_bg_color": "#ffffff",
      "badge_text_color": "#212121",
      "show_sold_out_badge": true,
      "show_automatic_new_badge": false,
      "badge_new_date_limit": 5,
      "form_style": "modern",
      "show_variant_image": false,
      "final_sale": true,
      "swatch_size": "regular",
      "swatch_style": "circle",
      "sibling_style": "image",
      "collection_swatch_style": "text-slider",
      "swatch_color_list": "Black: #000000\nWhite: #fafafa\nBlank: blank.png\nBaby Blue: #D0D9E1\nBlush: #DBB9AE\nBurgundy: #4C1523\nCharcoal: #3A3838\nLilac: #DDCFD8\nMustard: #C08035\nRose: #9D4D4A\nTaupe: #AA8880\nTurquoise: #0389B9\nGreen: #698656\nGrey: #C6B8A8\nOlive: #758C7D\nRed: #D05645\nPurple: #BBB7CF\nYellow: #DDB400\nOrange: #E0834B\nBlue: #275E7E\nPink: #CD9485",
      "free_shipping_limit": "100",
      "show_lock_icon": true,
      "show_recently_viewed_products": true,
      "header_link_hover": "#AB8C52",
      "show_logo_bg": false,
      "checkout_logo_position": "left",
      "checkout_logo_size": "large",
      "checkout_body_background_color": "#fff",
      "checkout_input_background_color_mode": "white",
      "checkout_sidebar_background_color": "#f9f9f9",
      "checkout_heading_font": "Open Sans",
      "checkout_body_font": "Open Sans",
      "checkout_accent_color": "#ab8c52",
      "checkout_button_color": "#ab8c52",
      "checkout_error_color": "#c85c42",
      "sale_background_color": "#be563f",
      "content_for_index": [],
      "color_schemes": {
        "scheme-1": {
          "settings": {
            "section_text": "#000000",
            "section_bg": "#faf3e8",
            "section_bg_light": "#f4f2e2",
            "section_bg_gradient": "",
            "btn_primary_bg": "#dc954d",
            "btn_primary_text": "#000000",
            "btn_primary_border": "#dc954d",
            "btn_secondary_bg": "#12543f",
            "btn_secondary_text": "#ffffff",
            "btn_secondary_border": "#12543f",
            "accent": "#dc954d",
            "lines_and_border": "#faead3",
            "overlay": "#000000",
            "links": "#000000"
          }
        },
        "scheme-2": {
          "settings": {
            "section_text": "#ffffff",
            "section_bg": "#dc954d",
            "section_bg_light": "#f4f2e2",
            "section_bg_gradient": "",
            "btn_primary_bg": "#eed9c2",
            "btn_primary_text": "#000000",
            "btn_primary_border": "#eed9c2",
            "btn_secondary_bg": "#ecb935",
            "btn_secondary_text": "#000000",
            "btn_secondary_border": "#ecb935",
            "accent": "#ffffff",
            "lines_and_border": "#f3f3f3",
            "overlay": "#000000",
            "links": "#ffffff"
          }
        },
        "scheme-3": {
          "settings": {
            "section_text": "#000000",
            "section_bg": "#eed9c2",
            "section_bg_light": "#faf3e8",
            "section_bg_gradient": "",
            "btn_primary_bg": "#dc954d",
            "btn_primary_text": "#000000",
            "btn_primary_border": "#dc954d",
            "btn_secondary_bg": "#12543f",
            "btn_secondary_text": "#ffffff",
            "btn_secondary_border": "#12543f",
            "accent": "#ecb935",
            "lines_and_border": "#dc954d",
            "overlay": "#000000",
            "links": "#000000"
          }
        },
        "scheme-4": {
          "settings": {
            "section_text": "#000000",
            "section_bg": "#faf3e8",
            "section_bg_light": "#eed9c2",
            "section_bg_gradient": "",
            "btn_primary_bg": "#be979f",
            "btn_primary_text": "#000000",
            "btn_primary_border": "#be979f",
            "btn_secondary_bg": "#dc954d",
            "btn_secondary_text": "#000000",
            "btn_secondary_border": "#dc954d",
            "accent": "#f7ddba",
            "lines_and_border": "#dc954d",
            "overlay": "#000000",
            "links": "#12543f"
          }
        },
        "scheme-5": {
          "settings": {
            "section_text": "#faf3e8",
            "section_bg": "#4b2e11",
            "section_bg_light": "#ab8660",
            "section_bg_gradient": "",
            "btn_primary_bg": "#f7ddba",
            "btn_primary_text": "#000000",
            "btn_primary_border": "#f7ddba",
            "btn_secondary_bg": "#d1def9",
            "btn_secondary_text": "#000000",
            "btn_secondary_border": "#d1def9",
            "accent": "#dc954d",
            "lines_and_border": "#faf3e8",
            "overlay": "#000000",
            "links": "#faf3e8"
          }
        },
        "scheme-6": {
          "settings": {
            "section_text": "#212121",
            "section_bg": "#ab8660",
            "section_bg_light": "#faf3e8",
            "section_bg_gradient": "",
            "btn_primary_bg": "#dc954d",
            "btn_primary_text": "#000000",
            "btn_primary_border": "#dc954d",
            "btn_secondary_bg": "#d1def9",
            "btn_secondary_text": "#000000",
            "btn_secondary_border": "#d1def9",
            "accent": "#ab8c52",
            "lines_and_border": "#f3f3f3",
            "overlay": "#000000",
            "links": "#282c2e"
          }
        },
        "scheme-7": {
          "settings": {
            "section_text": "#000000",
            "section_bg": "#faf3e8",
            "section_bg_light": "#f4f2e2",
            "section_bg_gradient": "",
            "btn_primary_bg": "#de8d9b",
            "btn_primary_text": "#000000",
            "btn_primary_border": "#de8d9b",
            "btn_secondary_bg": "#dc954d",
            "btn_secondary_text": "#000000",
            "btn_secondary_border": "#dc954d",
            "accent": "#f7ddba",
            "lines_and_border": "#dc954d",
            "overlay": "#000000",
            "links": "#000000"
          }
        },
        "scheme-8": {
          "settings": {
            "section_text": "#000000",
            "section_bg": "#f7ddba",
            "section_bg_light": "#dc954d",
            "section_bg_gradient": "",
            "btn_primary_bg": "#be979f",
            "btn_primary_text": "#000000",
            "btn_primary_border": "#be979f",
            "btn_secondary_bg": "#4b2e11",
            "btn_secondary_text": "#ffffff",
            "btn_secondary_border": "#4b2e11",
            "accent": "#eed9c2",
            "lines_and_border": "#ab8660",
            "overlay": "#000000",
            "links": "#000000"
          }
        },
        "scheme-9": {
          "settings": {
            "section_text": "#ffffff",
            "section_bg": "#000000",
            "section_bg_light": "#212121",
            "section_bg_gradient": "",
            "btn_primary_bg": "#dc954d",
            "btn_primary_text": "#000000",
            "btn_primary_border": "#dc954d",
            "btn_secondary_bg": "#12543f",
            "btn_secondary_text": "#ffffff",
            "btn_secondary_border": "#12543f",
            "accent": "#dc954d",
            "lines_and_border": "#ffffff",
            "overlay": "#000000",
            "links": "#ffffff"
          }
        },
        "scheme-10": {
          "settings": {
            "section_text": "#ffffff",
            "section_bg": "#000000",
            "section_bg_light": "#212121",
            "section_bg_gradient": "",
            "btn_primary_bg": "#dc954d",
            "btn_primary_text": "#000000",
            "btn_primary_border": "#dc954d",
            "btn_secondary_bg": "#12543f",
            "btn_secondary_text": "#ffffff",
            "btn_secondary_border": "#12543f",
            "accent": "#dc954d",
            "lines_and_border": "#ffffff",
            "overlay": "#000000",
            "links": "#ffffff"
          }
        },
        "scheme-11": {
          "settings": {
            "section_text": "#ffffff",
            "section_bg": "#000000",
            "section_bg_light": "#212121",
            "section_bg_gradient": "",
            "btn_primary_bg": "#dc954d",
            "btn_primary_text": "#000000",
            "btn_primary_border": "#dc954d",
            "btn_secondary_bg": "#12543f",
            "btn_secondary_text": "#ffffff",
            "btn_secondary_border": "#12543f",
            "accent": "#dc954d",
            "lines_and_border": "#ffffff",
            "overlay": "#000000",
            "links": "#ffffff"
          }
        }
      }
    },
    "Bold": {
      "color_scheme": "scheme-1",
      "menu_bg_color": "#ffffff",
      "header_link": "#212121",
      "submenu_bg_color": "#ffffff",
      "submenu_link_color": "#212121",
      "menu_transparent_color": "#ffffff",
      "sale_color": "#001da3",
      "type_nav_font": "source_code_pro_n4",
      "nav_font_size": 14,
      "type_base_font": "source_code_pro_n4",
      "base_font_size": 14,
      "type_subheading_font": "source_code_pro_n4",
      "sub_letter_spacing": 100,
      "subheading_size_desktop": 12,
      "type_header_font": "instrument_sans_n5",
      "heading_mini": 18,
      "heading_x_small": 22,
      "heading_small": 34,
      "heading_medium": 46,
      "heading_large": 60,
      "heading_x_large": 72,
      "heading_mini_mobile": 18,
      "heading_x_small_mobile": 22,
      "heading_small_mobile": 24,
      "heading_medium_mobile": 30,
      "heading_large_mobile": 40,
      "heading_x_large_mobile": 50,
      "type_btn_font": "instrument_sans_n4",
      "btn_border_style": "pill",
      "btn_size": 12,
      "grid_style": "classic",
      "dots_style": "circle",
      "product_grid_outline": true,
      "enable_superscript": true,
      "show_scroll_top_button": true,
      "instagram_link": "https://instagram.com/shopify",
      "facebook_link": "https://facebook.com/shopify",
      "tiktok_link": "https://tiktok.com/@shopify",
      "overlay_text": false,
      "product_grid_aspect_ratio": 1,
      "sale_bg_color": "#001da3",
      "sale_text_color": "#ffffff",
      "badge_bg_color": "#f7f9fa",
      "badge_text_color": "#001da3",
      "form_style": "classic",
      "show_labels": true,
      "variant_lines": true,
      "show_variant_image": true,
      "variant_image_style": "stacked",
      "final_sale": true,
      "collection_swatch_style": "text",
      "free_shipping_limit": "200",
      "show_lock_icon": true,
      "enable_accept_terms": false,
      "empty_cart_products": [],
      "text_color": "#212121",
      "bg_color": "#ffffff",
      "secondary_bg_color": "#F7F9FA",
      "accent_bright": "#001da3",
      "link_color": "#212121",
      "site_border_color": "#0b0b0b",
      "header_bg": "#ffffff",
      "menu_link_color": "#212121",
      "footer_bg": "#ffffff",
      "footer_border": "#212121",
      "footer_text": "#212121",
      "footer_link": "#212121",
      "type_heading_size": 115,
      "type_sub_size": 95,
      "type_body_size": 100,
      "btn_primary_bg_color": "#001da3",
      "btn_primary_text_color": "#ffffff",
      "btn_primary_border_color": "#001da3",
      "btn_secondary_bg_color": "#e5ff01",
      "btn_secondary_text_color": "#000000",
      "btn_secondary_border_color": "#000000",
      "checkout_logo_position": "left",
      "checkout_logo_size": "medium",
      "checkout_body_background_color": "#fff",
      "checkout_input_background_color_mode": "white",
      "checkout_sidebar_background_color": "#fafafa",
      "checkout_heading_font": "Open Sans",
      "checkout_body_font": "Open Sans",
      "checkout_accent_color": "#c5aa76",
      "checkout_button_color": "#c5aa76",
      "checkout_error_color": "#c85c42",
      "product_grid_show_rating": true,
      "enable_order_notes": true,
      "show_view_cart_button": true,
      "show_free_shipping_message": true,
      "message": "You are ||amount|| away from free shipping.",
      "link_hover_color": "#bcb2a8",
      "header_link_hover": "#AB8C52",
      "show_logo_bg": false,
      "menu_link_hover_color": "#AB8C52",
      "submenu_link_hover_color": "#AB8C52",
      "footer_link_hover": "#212121",
      "enable_free_shipping_message": true,
      "sale_background_color": "#be563f",
      "content_for_index": [],
      "color_schemes": {
        "scheme-1": {
          "settings": {
            "section_text": "#212121",
            "section_bg": "#ffffff",
            "section_bg_light": "#f7f9fa",
            "section_bg_gradient": "",
            "btn_primary_bg": "#001da3",
            "btn_primary_text": "#ffffff",
            "btn_primary_border": "#001da3",
            "btn_secondary_bg": "#e5ff01",
            "btn_secondary_text": "#000000",
            "btn_secondary_border": "#000000",
            "accent": "#001da3",
            "lines_and_border": "#000000",
            "overlay": "#000000",
            "links": "#000000"
          }
        },
        "scheme-2": {
          "settings": {
            "section_text": "#000000",
            "section_bg": "#f7f9fa",
            "section_bg_light": "#ffffff",
            "section_bg_gradient": "",
            "btn_primary_bg": "#001da3",
            "btn_primary_text": "#ffffff",
            "btn_primary_border": "#001da3",
            "btn_secondary_bg": "#ffffff",
            "btn_secondary_text": "#001da3",
            "btn_secondary_border": "#001da3",
            "accent": "#e5ff01",
            "lines_and_border": "#d1d1d1",
            "overlay": "#000000",
            "links": "#000000"
          }
        },
        "scheme-3": {
          "settings": {
            "section_text": "#ffffff",
            "section_bg": "#000000",
            "section_bg_light": "#001da3",
            "section_bg_gradient": "",
            "btn_primary_bg": "#001da3",
            "btn_primary_text": "#ffffff",
            "btn_primary_border": "#001da3",
            "btn_secondary_bg": "#e5ff01",
            "btn_secondary_text": "#000000",
            "btn_secondary_border": "#e5ff01",
            "accent": "#001da3",
            "lines_and_border": "#ffffff",
            "overlay": "#000000",
            "links": "#ffffff"
          }
        },
        "scheme-4": {
          "settings": {
            "section_text": "#000000",
            "section_bg": "#bbcad9",
            "section_bg_light": "#f7f9fa",
            "section_bg_gradient": "",
            "btn_primary_bg": "#e5ff01",
            "btn_primary_text": "#000000",
            "btn_primary_border": "#e5ff01",
            "btn_secondary_bg": "#001da3",
            "btn_secondary_text": "#ffffff",
            "btn_secondary_border": "#001da3",
            "accent": "#ffffff",
            "lines_and_border": "#001da3",
            "overlay": "#000000",
            "links": "#001da3"
          }
        },
        "scheme-5": {
          "settings": {
            "section_text": "#061c81",
            "section_bg": "#ffffff",
            "section_bg_light": "#e7ebff",
            "section_bg_gradient": "",
            "btn_primary_bg": "#061c81",
            "btn_primary_text": "#ffffff",
            "btn_primary_border": "#061c81",
            "btn_secondary_bg": "#fed602",
            "btn_secondary_text": "#000000",
            "btn_secondary_border": "#fed602",
            "accent": "#e7ebff",
            "lines_and_border": "#061c81",
            "overlay": "#000000",
            "links": "#061c81"
          }
        },
        "scheme-6": {
          "settings": {
            "section_text": "#ffffff",
            "section_bg": "#061c81",
            "section_bg_light": "#fed602",
            "section_bg_gradient": "",
            "btn_primary_bg": "#fed602",
            "btn_primary_text": "#000000",
            "btn_primary_border": "#fed602",
            "btn_secondary_bg": "#e7ebff",
            "btn_secondary_text": "#000000",
            "btn_secondary_border": "#e7ebff",
            "accent": "#fed602",
            "lines_and_border": "#ffffff",
            "overlay": "#000000",
            "links": "#ffffff"
          }
        },
        "scheme-7": {
          "settings": {
            "section_text": "#000000",
            "section_bg": "#e5ff01",
            "section_bg_light": "#ffffff",
            "section_bg_gradient": "",
            "btn_primary_bg": "#001da3",
            "btn_primary_text": "#ffffff",
            "btn_primary_border": "#001da3",
            "btn_secondary_bg": "#e7ebff",
            "btn_secondary_text": "#000000",
            "btn_secondary_border": "#e7ebff",
            "accent": "#ffffff",
            "lines_and_border": "#000000",
            "overlay": "#ffffff",
            "links": "#000000"
          }
        },
        "scheme-8": {
          "settings": {
            "section_text": "#ffffff",
            "section_bg": "#001da3",
            "section_bg_light": "#f7f9fa",
            "section_bg_gradient": "",
            "btn_primary_bg": "#f7f9fa",
            "btn_primary_text": "#000000",
            "btn_primary_border": "#f7f9fa",
            "btn_secondary_bg": "#e5ff01",
            "btn_secondary_text": "#000000",
            "btn_secondary_border": "#e5ff01",
            "accent": "#e5ff01",
            "lines_and_border": "#ffffff",
            "overlay": "#000000",
            "links": "#ffffff"
          }
        },
        "scheme-9": {
          "settings": {
            "section_text": "#ffffff",
            "section_bg": "#001da3",
            "section_bg_light": "#f7f9fa",
            "section_bg_gradient": "",
            "btn_primary_bg": "#f7f9fa",
            "btn_primary_text": "#000000",
            "btn_primary_border": "#f7f9fa",
            "btn_secondary_bg": "#e5ff01",
            "btn_secondary_text": "#000000",
            "btn_secondary_border": "#e5ff01",
            "accent": "#e5ff01",
            "lines_and_border": "#ffffff",
            "overlay": "#000000",
            "links": "#ffffff"
          }
        },
        "scheme-10": {
          "settings": {
            "section_text": "#ffffff",
            "section_bg": "#001da3",
            "section_bg_light": "#f7f9fa",
            "section_bg_gradient": "",
            "btn_primary_bg": "#f7f9fa",
            "btn_primary_text": "#000000",
            "btn_primary_border": "#f7f9fa",
            "btn_secondary_bg": "#e5ff01",
            "btn_secondary_text": "#000000",
            "btn_secondary_border": "#e5ff01",
            "accent": "#e5ff01",
            "lines_and_border": "#ffffff",
            "overlay": "#000000",
            "links": "#ffffff"
          }
        },
        "scheme-11": {
          "settings": {
            "section_text": "#ffffff",
            "section_bg": "#001da3",
            "section_bg_light": "#f7f9fa",
            "section_bg_gradient": "",
            "btn_primary_bg": "#f7f9fa",
            "btn_primary_text": "#000000",
            "btn_primary_border": "#f7f9fa",
            "btn_secondary_bg": "#e5ff01",
            "btn_secondary_text": "#000000",
            "btn_secondary_border": "#e5ff01",
            "accent": "#e5ff01",
            "lines_and_border": "#ffffff",
            "overlay": "#000000",
            "links": "#ffffff"
          }
        }
      }
    },
    "Groove": {
      "color_scheme": "scheme-1",
      "menu_bg_color": "#f2efe7",
      "header_link": "#212121",
      "submenu_bg_color": "#f2efe7",
      "submenu_link_color": "#212121",
      "menu_transparent_color": "#ffffff",
      "sale_color": "#f1a843",
      "type_nav_font": "dm_sans_n4",
      "nav_font_size": 16,
      "type_nav_caps": false,
      "type_base_font": "dm_sans_n4",
      "base_font_size": 16,
      "type_subheading_font": "dm_sans_n4",
      "sub_letter_spacing": 200,
      "type_sub_uppercase": true,
      "subheading_size_desktop": 11,
      "subheading_size_mobile": 12,
      "type_header_font": "bricolage_grotesque_n4",
      "heading_letter_spacing": 0,
      "heading_mini": 14,
      "heading_x_small": 20,
      "heading_small": 30,
      "heading_medium": 40,
      "heading_large": 52,
      "heading_x_large": 70,
      "heading_x_small_mobile": 16,
      "heading_small_mobile": 20,
      "heading_medium_mobile": 30,
      "heading_large_mobile": 40,
      "heading_x_large_mobile": 50,
      "type_btn_font": "dm_sans_n7",
      "btn_border_style": "pill",
      "btn_size": 11,
      "dots_style": "circle",
      "parallax_enable": true,
      "product_grid_outline": false,
      "enable_superscript": true,
      "show_scroll_top_button": true,
      "instagram_link": "https://instagram.com/shopify",
      "tiktok_link": "https://tiktok.com/@shopify",
      "product_grid_aspect_ratio": 1.2,
      "sale_bg_color": "#f1a843",
      "sale_text_color": "#fafafa",
      "badge_bg_color": "#ffffff",
      "badge_text_color": "#212121",
      "show_sold_out_badge": true,
      "show_automatic_new_badge": false,
      "badge_new_date_limit": 5,
      "show_cutline": true,
      "cutline_color": "",
      "form_style": "modern",
      "show_variant_image": false,
      "final_sale": true,
      "swatch_size": "regular",
      "swatch_style": "square",
      "sibling_style": "image",
      "collection_swatch_style": "text-slider",
      "swatch_color_list": "Black: #000000\nWhite: #fafafa\nBlank: blank.png",
      "free_shipping_limit": "100",
      "show_lock_icon": false,
      "show_recently_viewed_products": true,
      "currency_code_enable": false,
      "header_link_hover": "#AB8C52",
      "show_logo_bg": false,
      "checkout_logo_position": "left",
      "checkout_logo_size": "large",
      "checkout_body_background_color": "#fff",
      "checkout_input_background_color_mode": "white",
      "checkout_sidebar_background_color": "#f9f9f9",
      "checkout_heading_font": "Open Sans",
      "checkout_body_font": "Open Sans",
      "checkout_accent_color": "#ab8c52",
      "checkout_button_color": "#ab8c52",
      "checkout_error_color": "#c85c42",
      "sale_background_color": "#be563f",
      "content_for_index": [],
      "color_schemes": {
        "scheme-1": {
          "settings": {
            "section_text": "#212121",
            "section_bg": "#f2efe7",
            "section_bg_light": "#e7e0cf",
            "section_bg_gradient": "",
            "btn_primary_bg": "#343d66",
            "btn_primary_text": "#ffffff",
            "btn_primary_border": "#343d66",
            "btn_secondary_bg": "#f7f7f5",
            "btn_secondary_text": "#000000",
            "btn_secondary_border": "#f7f7f5",
            "accent": "#7482bf",
            "lines_and_border": "#e7e0cf",
            "overlay": "#000000",
            "links": "#212121"
          }
        },
        "scheme-2": {
          "settings": {
            "section_text": "#ffffff",
            "section_bg": "#212121",
            "section_bg_light": "#f5f2ec",
            "section_bg_gradient": "",
            "btn_primary_bg": "#212121",
            "btn_primary_text": "#ffffff",
            "btn_primary_border": "#212121",
            "btn_secondary_bg": "#ffffff",
            "btn_secondary_text": "#000000",
            "btn_secondary_border": "#ab8c52",
            "accent": "#ab8c52",
            "lines_and_border": "#f3f3f3",
            "overlay": "#000000",
            "links": "#ffffff"
          }
        },
        "scheme-3": {
          "settings": {
            "section_text": "#212121",
            "section_bg": "#f5f2ec",
            "section_bg_light": "#f5f2ec",
            "section_bg_gradient": "",
            "btn_primary_bg": "#212121",
            "btn_primary_text": "#ffffff",
            "btn_primary_border": "#212121",
            "btn_secondary_bg": "#ab8c52",
            "btn_secondary_text": "#ffffff",
            "btn_secondary_border": "#ab8c52",
            "accent": "#ab8c52",
            "lines_and_border": "#dcdcdc",
            "overlay": "#000000",
            "links": "#282c2e"
          }
        },
        "scheme-4": {
          "settings": {
            "section_text": "#ffffff",
            "section_bg": "#b0a38b",
            "section_bg_light": "#f5f2ec",
            "section_bg_gradient": "",
            "btn_primary_bg": "#212121",
            "btn_primary_text": "#ffffff",
            "btn_primary_border": "#ffffff",
            "btn_secondary_bg": "#ab8c52",
            "btn_secondary_text": "#ffffff",
            "btn_secondary_border": "#ab8c52",
            "accent": "#ab8c52",
            "lines_and_border": "#f3f3f3",
            "overlay": "#000000",
            "links": "#282c2e"
          }
        },
        "scheme-5": {
          "settings": {
            "section_text": "#000000",
            "section_bg": "#e7e0cf",
            "section_bg_light": "#f5f2ec",
            "section_bg_gradient": "",
            "btn_primary_bg": "#212121",
            "btn_primary_text": "#ffffff",
            "btn_primary_border": "#212121",
            "btn_secondary_bg": "#ab8c52",
            "btn_secondary_text": "#ffffff",
            "btn_secondary_border": "#ab8c52",
            "accent": "#ab8c52",
            "lines_and_border": "#343d66",
            "overlay": "#000000",
            "links": "#282c2e"
          }
        },
        "scheme-6": {
          "settings": {
            "section_text": "#ffffff",
            "section_bg": "#7482be",
            "section_bg_light": "#f5f2ec",
            "section_bg_gradient": "",
            "btn_primary_bg": "#ffffff",
            "btn_primary_text": "#212121",
            "btn_primary_border": "#ffffff",
            "btn_secondary_bg": "#ab8c52",
            "btn_secondary_text": "#ffffff",
            "btn_secondary_border": "#ab8c52",
            "accent": "#ab8c52",
            "lines_and_border": "#f3f3f3",
            "overlay": "#000000",
            "links": "#282c2e"
          }
        },
        "scheme-7": {
          "settings": {
            "section_text": "#212121",
            "section_bg": "#f5f2ec",
            "section_bg_light": "#f5f2ec",
            "section_bg_gradient": "",
            "btn_primary_bg": "#212121",
            "btn_primary_text": "#ffffff",
            "btn_primary_border": "#212121",
            "btn_secondary_bg": "#ab8c52",
            "btn_secondary_text": "#ffffff",
            "btn_secondary_border": "#ab8c52",
            "accent": "#ab8c52",
            "lines_and_border": "#f7f7f5",
            "overlay": "#000000",
            "links": "#000000"
          }
        },
        "scheme-8": {
          "settings": {
            "section_text": "#212121",
            "section_bg": "#eae6dd",
            "section_bg_light": "#f5f2ec",
            "section_bg_gradient": "",
            "btn_primary_bg": "#212121",
            "btn_primary_text": "#ffffff",
            "btn_primary_border": "#212121",
            "btn_secondary_bg": "#ab8c52",
            "btn_secondary_text": "#ffffff",
            "btn_secondary_border": "#ab8c52",
            "accent": "#ab8c52",
            "lines_and_border": "#212121",
            "overlay": "#000000",
            "links": "#282c2e"
          }
        },
        "scheme-9": {
          "settings": {
            "section_text": "#212121",
            "section_bg": "#e7e0cf",
            "section_bg_light": "#f5f2ec",
            "section_bg_gradient": "",
            "btn_primary_bg": "#212121",
            "btn_primary_text": "#ffffff",
            "btn_primary_border": "#212121",
            "btn_secondary_bg": "#ab8c52",
            "btn_secondary_text": "#ffffff",
            "btn_secondary_border": "#ab8c52",
            "accent": "#ab8c52",
            "lines_and_border": "#f3f3f3",
            "overlay": "#000000",
            "links": "#282c2e"
          }
        },
        "scheme-10": {
          "settings": {
            "section_text": "#f7f7f5",
            "section_bg": "#343d66",
            "section_bg_light": "#f5f2ec",
            "section_bg_gradient": "",
            "btn_primary_bg": "#343d66",
            "btn_primary_text": "#ffffff",
            "btn_primary_border": "#000000",
            "btn_secondary_bg": "#f7f7f5",
            "btn_secondary_text": "#000000",
            "btn_secondary_border": "#ab8c52",
            "accent": "#ab8c52",
            "lines_and_border": "#f3f3f3",
            "overlay": "#000000",
            "links": "#f7f7f5"
          }
        },
        "scheme-11": {
          "settings": {
            "section_text": "#343d66",
            "section_bg": "#f2efe7",
            "section_bg_light": "#f2efe7",
            "section_bg_gradient": "",
            "btn_primary_bg": "#212121",
            "btn_primary_text": "#ffffff",
            "btn_primary_border": "#212121",
            "btn_secondary_bg": "#ab8c52",
            "btn_secondary_text": "#ffffff",
            "btn_secondary_border": "#ab8c52",
            "accent": "#ab8c52",
            "lines_and_border": "#343d66",
            "overlay": "#000000",
            "links": "#282c2e"
          }
        }
      }
    }
  }
}
