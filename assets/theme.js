/*
* @license
* Broadcast Theme (c) Invisible Themes
*
* The contents of this file should not be modified.
* add any minor changes to assets/custom.js
*
*/
!function(t){"use strict";!function(){const t={NODE_ENV:"production"};try{if(process)return process.env=Object.assign({},process.env),void Object.assign(process.env,t)}catch(t){}globalThis.process={env:t}}(),window.theme=window.theme||{},window.theme.sizes={mobile:480,small:750,large:990,widescreen:1400},window.theme.focusable='button, [href], input, select, textarea, [tabindex]:not([tabindex="-1"])',window.theme.getWindowWidth=function(){return document.documentElement.clientWidth||document.body.clientWidth||window.innerWidth},window.theme.getWindowHeight=function(){return document.documentElement.clientHeight||document.body.clientHeight||window.innerHeight},window.theme.isMobile=function(){return window.theme.getWindowWidth()<window.theme.sizes.small};window.theme.formatMoney=function(t,e){"string"==typeof t&&(t=t.replace(".",""));let s="";const i=/\{\{\s*(\w+)\s*\}\}/,o=e||"${{amount}}";function n(t,e=2,s=",",i="."){if(isNaN(t)||null==t)return 0;const o=(t=(t/100).toFixed(e)).split(".");return o[0].replace(/(\d)(?=(\d\d\d)+(?!\d))/g,`$1${s}`)+(o[1]?i+o[1]:"")}switch(o.match(i)[1]){case"amount":s=n(t,2);break;case"amount_no_decimals":s=n(t,0);break;case"amount_with_comma_separator":s=n(t,2,".",",");break;case"amount_no_decimals_with_comma_separator":s=n(t,0,".",",");break;case"amount_with_apostrophe_separator":s=n(t,2,"'",".");break;case"amount_no_decimals_with_space_separator":s=n(t,0," ","");break;case"amount_with_space_separator":s=n(t,2," ",",");break;case"amount_with_period_and_space_separator":s=n(t,2," ",".")}return o.replace(i,s)},window.theme.debounce=function(t,e){let s;return function(){if(t){const i=()=>t.apply(this,arguments);clearTimeout(s),s=setTimeout(i,e)}}};let e=o(),s=!0;function i(){const{windowHeight:t,headerHeight:i,logoHeight:n,footerHeight:r,collectionNavHeight:a}=window.theme.readHeights(),l=o();(!s||l!==e||window.innerWidth>window.theme.sizes.mobile)&&(document.documentElement.style.setProperty("--full-height",`${t}px`),document.documentElement.style.setProperty("--three-quarters",t*(3/4)+"px"),document.documentElement.style.setProperty("--two-thirds",t*(2/3)+"px"),document.documentElement.style.setProperty("--one-half",t/2+"px"),document.documentElement.style.setProperty("--one-third",t/3+"px"),e=l,s=!1),document.documentElement.style.setProperty("--collection-nav-height",`${a}px`),document.documentElement.style.setProperty("--header-height",`${i}px`),document.documentElement.style.setProperty("--footer-height",`${r}px`),document.documentElement.style.setProperty("--content-full",t-i-n/2+"px"),document.documentElement.style.setProperty("--content-min",t-i-r+"px")}function o(){return window.matchMedia("(orientation: portrait)").matches?"portrait":window.matchMedia("(orientation: landscape)").matches?"landscape":void 0}function n(t){const e=document.querySelector(t);return e?e.offsetHeight:0}window.theme.readHeights=function(){const t={};return t.windowHeight=Math.min(window.screen.height,window.innerHeight),t.footerHeight=n('[data-section-type*="footer"]'),t.headerHeight=n("[data-header-height]"),t.stickyHeaderHeight=document.querySelector("[data-header-sticky]")?t.headerHeight:0,t.collectionNavHeight=n("[data-collection-nav]"),t.logoHeight=function(){const t=n("[data-footer-logo]");return t>0?t+20:0}(),t},i(),window.addEventListener("DOMContentLoaded",i),document.addEventListener("theme:resize",i),document.addEventListener("shopify:section:load",i),window.theme.scrollTo=t=>{const e=document.querySelector("[data-header-sticky]")?document.querySelector("[data-header-height]").offsetHeight:0;window.scrollTo({top:t+window.scrollY-e,left:0,behavior:"smooth"})};const r={},a={forceFocus(t,e){e=e||{};var s=t.tabIndex;t.tabIndex=-1,t.dataset.tabIndex=s,t.focus(),void 0!==e.className&&t.classList.add(e.className),t.addEventListener("blur",(function i(o){o.target.removeEventListener(o.type,i),t.tabIndex=s,delete t.dataset.tabIndex,void 0!==e.className&&t.classList.remove(e.className)}))},focusHash(t){t=t||{};var e=window.location.hash,s=document.getElementById(e.slice(1));if(s&&t.ignore&&s.matches(t.ignore))return!1;e&&s&&this.forceFocus(s,t)},bindInPageLinks(t){return t=t||{},Array.prototype.slice.call(document.querySelectorAll('a[href^="#"]')).filter((e=>{if("#"===e.hash||""===e.hash)return!1;if(t.ignore&&e.matches(t.ignore))return!1;if(s=e.hash.substr(1),null===document.getElementById(s))return!1;var s,i=document.querySelector(e.hash);return!!i&&(e.addEventListener("click",(()=>{this.forceFocus(i,t)})),!0)}))},focusable(t){return Array.prototype.slice.call(t.querySelectorAll("[tabindex],[draggable],a[href],area,button:enabled,input:not([type=hidden]):enabled,object,select:enabled,textarea:enabled")).filter((t=>!(!(t.offsetWidth||t.offsetHeight||t.getClientRects().length)||!this.isVisible(t))))},trapFocus(t,e){e=e||{};var s=this.focusable(t),i=e.elementToFocus||t,o=s[0],n=s[s.length-1];this.removeTrapFocus(),r.focusin=function(e){t!==e.target&&!t.contains(e.target)&&o&&o===e.target&&o.focus(),e.target!==t&&e.target!==n&&e.target!==o||document.addEventListener("keydown",r.keydown)},r.focusout=function(){document.removeEventListener("keydown",r.keydown)},r.keydown=function(e){"Tab"===e.code&&(e.target!==n||e.shiftKey||(e.preventDefault(),o.focus()),e.target!==t&&e.target!==o||!e.shiftKey||(e.preventDefault(),n.focus()))},document.addEventListener("focusout",r.focusout),document.addEventListener("focusin",r.focusin),this.forceFocus(i,e)},removeTrapFocus(){document.removeEventListener("focusin",r.focusin),document.removeEventListener("focusout",r.focusout),document.removeEventListener("keydown",r.keydown)},autoFocusLastElement(){window.a11y.lastElement&&document.body.classList.contains("is-focused")&&setTimeout((()=>{var t;null===(t=window.a11y.lastElement)||void 0===t||t.focus()}))},accessibleLinks(t,e){if("string"!=typeof t)throw new TypeError(t+" is not a String.");if(0!==(t=document.querySelectorAll(t)).length){(e=e||{}).messages=e.messages||{};var s,i,o,n={newWindow:e.messages.newWindow||"Opens in a new window.",external:e.messages.external||"Opens external website.",newWindowExternal:e.messages.newWindowExternal||"Opens external website in a new window."},r=e.prefix||"a11y",a={newWindow:r+"-new-window-message",external:r+"-external-message",newWindowExternal:r+"-new-window-external-message"};t.forEach((t=>{var e=t.getAttribute("target"),s=t.getAttribute("rel"),i=function(t){return t.hostname!==window.location.hostname}(t),o="_blank"===e,n=null===s||-1===s.indexOf("noopener");if(o&&n){var r=null===s?"noopener":s+" noopener";t.setAttribute("rel",r)}i&&o?t.setAttribute("aria-describedby",a.newWindowExternal):i?t.setAttribute("aria-describedby",a.external):o&&t.setAttribute("aria-describedby",a.newWindow)})),s=n,i=document.createElement("ul"),o=Object.keys(s).reduce(((t,e)=>t+"<li id="+a[e]+">"+s[e]+"</li>"),""),i.setAttribute("hidden",!0),i.innerHTML=o,document.body.appendChild(i)}},isVisible(t){var e=window.getComputedStyle(t);return"none"!==e.display&&"hidden"!==e.visibility}};function l(){if(document.querySelector("cart-items"))return;const t=document.createElement("cart-items");document.body.appendChild(t)}function d(t){t.querySelectorAll(".form-field").forEach((t=>{const e=t.querySelector("label"),s=t.querySelector("input, textarea");e&&s&&(s.addEventListener("keyup",(t=>{""!==t.target.value?e.classList.add("label--float"):e.classList.remove("label--float")})),s.value&&s.value.length&&e.classList.add("label--float"))}))}window.theme=window.theme||{},window.theme.a11y=a,window.theme.throttle=(t,e)=>{let s,i;return function o(...n){const r=Date.now();i=clearTimeout(i),!s||r-s>=e?(t.apply(null,n),s=r):i=setTimeout(o.bind(null,...n),e-(r-s))}};let c=window.theme.getWindowWidth(),h=window.theme.getWindowHeight();let u=window.scrollY,p=null,m=null,v=null,g=null,b=0;function w(e){setTimeout((()=>{b&&clearTimeout(b),t.disablePageScroll(e.detail,{allowTouchMove:t=>"TEXTAREA"===t.tagName}),document.documentElement.setAttribute("data-scroll-locked","")}))}function f(t){const e=t.detail;e?b=setTimeout(y,e):y()}function y(){t.clearQueueScrollLocks(),t.enablePageScroll(),document.documentElement.removeAttribute("data-scroll-locked")}const L=(t,e="",s)=>{const i=s||document.createElement("div");return i.classList.add(e),t.parentNode.insertBefore(i,t),i.appendChild(t)};function E(t){t.querySelectorAll(".rte table").forEach((t=>{L(t,"rte__table-wrapper"),t.setAttribute("data-scroll-lock-scrollable","")}));t.querySelectorAll('.rte iframe[src*="youtube.com/embed"], .rte iframe[src*="player.vimeo"], .rte iframe#admin_bar_iframe').forEach((t=>{L(t,"rte__video-wrapper")}))}function S(t){const e=t.querySelectorAll("[data-aria-toggle]");e.length&&e.forEach((t=>{t.addEventListener("click",(function(t){t.preventDefault();const e=t.currentTarget;e.setAttribute("aria-expanded","false"==e.getAttribute("aria-expanded")?"true":"false");const s=e.getAttribute("aria-controls"),i=document.querySelector(`#${s}`),o=()=>{i.classList.remove("expanding"),i.removeEventListener("transitionend",o)},n=()=>{i.classList.add("expanding"),i.removeEventListener("transitionstart",n)};i.addEventListener("transitionstart",n),i.addEventListener("transitionend",o),i.classList.toggle("expanded")}))}))}const A="is-loading",k="img.is-loading";const C="[data-aos]:not(.aos-animate)",q="[data-aos-anchor]",T="[data-aos]:not([data-aos-anchor]):not(.aos-animate)",M="aos-animate",x={attributes:!1,childList:!0,subtree:!0};let I=[];const O=t=>{for(const e of t)if("childList"===e.type){const t=e.target,s=t.querySelectorAll(C),i=t.querySelectorAll(q);s.length&&s.forEach((t=>{P.observe(t)})),i.length&&D(i)}},P=new IntersectionObserver(((t,e)=>{t.forEach((t=>{t.isIntersecting&&(t.target.classList.add(M),e.unobserve(t.target))}))}),{root:null,rootMargin:"0px",threshold:[0,.1,.25,.5,.75,1]}),H=new IntersectionObserver(((t,e)=>{t.forEach((t=>{if(t.intersectionRatio){const s=t.target.querySelectorAll(C);s.length&&s.forEach((t=>{t.classList.add(M)})),e.unobserve(t.target)}}))}),{root:null,rootMargin:"0px",threshold:[.1,.25,.5,.75,1]});function D(t){t.length&&t.forEach((t=>{const e=t.dataset.aosAnchor;if(e&&-1===I.indexOf(e)){const t=document.querySelector(e);t&&(H.observe(t),I.push(e))}}))}window.requestIdleCallback=window.requestIdleCallback||function(t){var e=Date.now();return setTimeout((function(){t({didTimeout:!1,timeRemaining:function(){return Math.max(0,50-(Date.now()-e))}})}),1)},window.cancelIdleCallback=window.cancelIdleCallback||function(t){clearTimeout(t)},window.theme.settings.enableAnimations&&(!function(){const t=document.querySelectorAll(T);t.length&&t.forEach((t=>{P.observe(t)}))}(),function(){const t=document.querySelectorAll(q);t.length&&D(t)}(),new MutationObserver(O).observe(document.body,x),document.addEventListener("shopify:section:unload",(t=>{var e;const s="#"+(null===(e=t.target.querySelector("[data-section-id]"))||void 0===e?void 0:e.id),i=I.indexOf(s);-1!==i&&I.splice(i,1)}))),window.addEventListener("resize",window.theme.debounce((function(){document.dispatchEvent(new CustomEvent("theme:resize",{bubbles:!0})),c!==window.theme.getWindowWidth()&&(document.dispatchEvent(new CustomEvent("theme:resize:width",{bubbles:!0})),c=window.theme.getWindowWidth()),h!==window.theme.getWindowHeight()&&(document.dispatchEvent(new CustomEvent("theme:resize:height",{bubbles:!0})),h=window.theme.getWindowHeight())}),50)),function(){let t;window.addEventListener("scroll",(function(){t&&window.cancelAnimationFrame(t),t=window.requestAnimationFrame((function(){!function(){const t=window.scrollY;t>u?(m=!0,p=!1):t<u?(m=!1,p=!0):(p=null,m=null),u=t,document.dispatchEvent(new CustomEvent("theme:scroll",{detail:{up:p,down:m,position:t},bubbles:!1})),p&&!v&&document.dispatchEvent(new CustomEvent("theme:scroll:up",{detail:{position:t},bubbles:!1})),m&&!g&&document.dispatchEvent(new CustomEvent("theme:scroll:down",{detail:{position:t},bubbles:!1})),g=m,v=p}()}))}),{passive:!0}),window.addEventListener("theme:scroll:lock",w),window.addEventListener("theme:scroll:unlock",f)}(),"ontouchstart"in window||navigator.maxTouchPoints>0||navigator.msMaxTouchPoints>0?(document.documentElement.className=document.documentElement.className.replace("no-touch","supports-touch"),window.theme.touch=!0):window.theme.touch=!1,document.addEventListener("load",(t=>{"img"==t.target.tagName.toLowerCase()&&t.target.classList.contains(A)&&(t.target.classList.remove(A),t.target.parentNode.classList.remove(A),t.target.parentNode.parentNode.classList.contains(A)&&t.target.parentNode.parentNode.classList.remove(A))}),!0),window.addEventListener("DOMContentLoaded",(()=>{S(document),d(document),E(document),document.querySelectorAll(k).forEach((t=>{t.complete&&(t.classList.remove(A),t.parentNode.classList.remove(A),t.parentNode.parentNode.classList.contains(A)&&t.parentNode.parentNode.classList.remove(A))})),document.body.classList.add("is-loaded"),l(),requestIdleCallback((()=>{Shopify.visualPreviewMode&&document.documentElement.classList.add("preview-mode")}))})),document.addEventListener("shopify:section:load",(t=>{const e=t.target;d(e),E(e),S(document)}));const F="is-focused",_="[data-skip-content]",$='a[href="#"]';function B(t){this.status=t.status||null,this.headers=t.headers||null,this.json=t.json||null,this.body=t.body||null}window.a11y=new class{init(){this.a11y=window.theme.a11y,this.html=document.documentElement,this.body=document.body,this.inPageLink=document.querySelector(_),this.linkesWithOnlyHash=document.querySelectorAll($),this.a11y.focusHash(),this.a11y.bindInPageLinks(),this.clickEvents(),this.focusEvents()}clickEvents(){this.inPageLink&&this.inPageLink.addEventListener("click",(t=>{t.preventDefault()})),this.linkesWithOnlyHash&&this.linkesWithOnlyHash.forEach((t=>{t.addEventListener("click",(t=>{t.preventDefault()}))}))}focusEvents(){document.addEventListener("mousedown",(()=>{this.body.classList.remove(F)})),document.addEventListener("keyup",(t=>{"Tab"===t.code&&this.body.classList.add(F)}))}constructor(){this.init()}},window.theme.waitForAnimationEnd=function(t){return new Promise((e=>{null==t||t.addEventListener("animationend",(function s(i){i.target==t&&(t.removeEventListener("animationend",s),e())}))}))},window.theme.waitForAllAnimationsEnd=function(t){return new Promise(((e,s)=>{const i=t.querySelectorAll("[data-aos]");let o=0;function n(t){o++,o===i.length&&e(),t.target.removeEventListener("animationend",n)}i.forEach((t=>{t.addEventListener("animationend",n)})),o||s()}))},B.prototype=Error.prototype;const W="is-animated",N="is-added",R="is-disabled",U="is-empty",V="is-hidden",j="is-hiding",z="is-loading",J="is-open",X="is-removed",Q="is-success",Y="is-visible",K="is-expanded",G="is-updated",Z="variant--soldout",tt="variant--unavailable",et="[data-api-content]",st="[data-api-line-items]",it="[data-api-upsell-items]",ot="[data-api-cart-price]",nt="[data-animation]",rt="[data-skip-upsell-product]",at="[data-add-to-cart-bar]",lt="[data-cart-error-close]",dt="cart-drawer",ct="[data-cart-drawer-close]",ht="[data-cart-empty]",ut="[data-cart-errors]",pt="[data-item-remove]",mt="[data-cart-page]",vt="[data-cart-form]",gt="[data-cart-acceptance-checkbox]",bt="[data-cart-checkout-buttons]",wt="[data-cart-checkout-button]",ft="[data-cart-total]",yt="[data-checkout-buttons]",Lt="[data-error-message]",Et="[data-close-error]",St="[data-cart-errors-container]",At="[data-form-wrapper]",kt="[data-free-shipping]",Ct="[data-progress-graph]",qt="[data-progress-bar]",Tt="[data-header-wrapper]",Mt="[data-item]",xt="[data-items-holder]",It="[data-left-to-spend]",Ot="[data-drawer]",Pt="[data-section-id]",Ht="[data-cart-price-holder]",Dt="[data-quick-add-holder]",Ft="[data-quick-add-modal]",_t='input[name="updates[]"]',$t="[data-upsell-products]",Bt="[data-upsell-widget]",Wt="[data-terms-error-message]",Nt="[data-collapsible-body]",Rt="noscript",Ut="data-cart-total",Vt="disabled",jt="data-free-shipping",zt="data-free-shipping-limit",Jt="data-item",Xt="data-item-index",Qt="data-item-title",Yt="open",Kt="data-quick-add-holder",Gt="data-scroll-locked",Zt="data-upsell-auto-open",te="name";let ee=class extends HTMLElement{connectedCallback(){this.cartPage=document.querySelector(mt),this.cartForm=document.querySelector(vt),this.cartDrawer=document.querySelector(dt),this.cartEmpty=document.querySelector(ht),this.cartTermsCheckbox=document.querySelector(gt),this.cartCheckoutButtonWrapper=document.querySelector(bt),this.cartCheckoutButton=document.querySelector(wt),this.checkoutButtons=document.querySelector(yt),this.itemsHolder=document.querySelector(xt),this.priceHolder=document.querySelector(Ht),this.items=document.querySelectorAll(Mt),this.cartTotal=document.querySelector(ft),this.freeShipping=document.querySelectorAll(kt),this.cartErrorHolder=document.querySelector(ut),this.cartCloseErrorMessage=document.querySelector(lt),this.headerWrapper=document.querySelector(Tt),this.navDrawer=document.querySelector(Ot),this.upsellProductsHolder=document.querySelector($t),this.subtotal=window.theme.subtotal,this.cart=this.cartDrawer||this.cartPage,this.animateItems=this.animateItems.bind(this),this.addToCart=this.addToCart.bind(this),this.cartAddEvent=this.cartAddEvent.bind(this),this.updateProgress=this.updateProgress.bind(this),this.onCartDrawerClose=this.onCartDrawerClose.bind(this),document.addEventListener("theme:cart:add",this.cartAddEvent),document.addEventListener("theme:announcement:init",this.updateProgress),"drawer"==theme.settings.cartType&&(document.addEventListener("theme:cart-drawer:open",this.animateItems),document.addEventListener("theme:cart-drawer:close",this.onCartDrawerClose)),this.skipUpsellProductsArray=[],this.skipUpsellProductEvent(),this.checkSkippedUpsellProductsFromStorage(),this.toggleCartUpsellWidgetVisibility(),this.circumference=28*Math.PI,this.freeShippingLimit=this.freeShipping.length?100*Number(this.freeShipping[0].getAttribute(zt))*window.Shopify.currency.rate:0,this.freeShippingMessageHandle(this.subtotal),this.updateProgress(),this.build=this.build.bind(this),this.updateCart=this.updateCart.bind(this),this.productAddCallback=this.productAddCallback.bind(this),this.formSubmitHandler=window.theme.throttle(this.formSubmitHandler.bind(this),50),this.cartPage&&this.animateItems(),this.cart&&(this.hasItemsInCart=this.hasItemsInCart.bind(this),this.cartCount=this.getCartItemCount()),this.toggleClassesOnContainers=this.toggleClassesOnContainers.bind(this),this.totalItems=this.items.length,this.cartUpdateFailed=!1,this.cartEvents(),this.cartRemoveEvents(),this.cartUpdateEvents(),document.addEventListener("theme:product:add",this.productAddCallback),document.addEventListener("theme:product:add-error",this.productAddCallback),document.addEventListener("theme:cart:refresh",this.getCart.bind(this))}disconnectedCallback(){document.removeEventListener("theme:cart:add",this.cartAddEvent),document.removeEventListener("theme:cart:refresh",this.cartAddEvent),document.removeEventListener("theme:announcement:init",this.updateProgress),document.removeEventListener("theme:product:add",this.productAddCallback),document.removeEventListener("theme:product:add-error",this.productAddCallback),document.documentElement.hasAttribute(Gt)&&document.dispatchEvent(new CustomEvent("theme:scroll:unlock",{bubbles:!0}))}onCartDrawerClose(){var t;this.resetAnimatedItems(),(null===(t=this.cartDrawer)||void 0===t?void 0:t.classList.contains(J))&&this.cart.classList.remove(G),this.cartEmpty.classList.remove(G),this.cartErrorHolder.classList.remove(K),this.cart.querySelectorAll(nt).forEach((t=>{const e=()=>{t.classList.remove(j),t.removeEventListener("animationend",e)};t.classList.add(j),t.addEventListener("animationend",e)}))}cartUpdateEvents(){this.items=document.querySelectorAll(Mt),this.items.forEach((t=>{t.addEventListener("theme:cart:update",(e=>{this.updateCart({id:e.detail.id,quantity:e.detail.quantity},t)}))}))}cartRemoveEvents(){document.querySelectorAll(pt).forEach((t=>{const e=t.closest(Mt);t.addEventListener("click",(s=>{s.preventDefault(),t.classList.contains(R)||this.updateCart({id:t.dataset.id,quantity:0},e)}))})),this.cartCloseErrorMessage&&this.cartCloseErrorMessage.addEventListener("click",(t=>{t.preventDefault(),this.cartErrorHolder.classList.remove(K)}))}cartAddEvent(t){let e="",s=t.detail.button;if(s.hasAttribute("disabled"))return;const i=s.closest("form");if(!i.checkValidity())return void i.reportValidity();e=new FormData(i);[...i.elements].some((t=>t.closest(Rt)))&&(e=this.handleFormDataDuplicates([...i.elements],e)),null!==i&&i.querySelector('[type="file"]')||("drawer"===theme.settings.cartType&&this.cartDrawer&&t.preventDefault(),this.addToCart(e,s))}handleFormDataDuplicates(t,e){return t.length&&"object"==typeof e?(t.forEach((t=>{if(t.closest(Rt)){const s=t.getAttribute(te),i=t.value;if(s){const t=e.getAll(s);t.length>1&&t.splice(t.indexOf(i),1),e.delete(s),e.set(s,t[0])}}})),e):e}cartEvents(){this.cartTermsCheckbox&&(this.cartTermsCheckbox.removeEventListener("change",this.formSubmitHandler),this.cartCheckoutButtonWrapper.removeEventListener("click",this.formSubmitHandler),this.cartForm.removeEventListener("submit",this.formSubmitHandler),this.cartTermsCheckbox.addEventListener("change",this.formSubmitHandler),this.cartCheckoutButtonWrapper.addEventListener("click",this.formSubmitHandler),this.cartForm.addEventListener("submit",this.formSubmitHandler))}formSubmitHandler(){const t=document.querySelector(gt).checked,e=document.querySelector(Wt);if(t)e.classList.remove(K),this.cartCheckoutButton.removeAttribute(Vt);else{if(document.querySelector(Wt).length>0)return;e.innerText=theme.strings.cartAcceptanceError,this.cartCheckoutButton.setAttribute(Vt,!0),e.classList.add(K)}}formErrorsEvents(t){const e=t.querySelector(Et);null==e||e.addEventListener("click",(e=>{e.preventDefault(),t&&t.classList.remove(Y)}))}getCart(){fetch(theme.routes.cart_url+"?section_id=api-cart-items").then(this.cartErrorsHandler).then((t=>t.text())).then((t=>{const e=document.createElement("div");e.innerHTML=t;const s=e.querySelector(et);this.build(s)})).catch((t=>console.log(t)))}addToCart(t,e){this.cart&&this.cart.classList.add(z);const s=null==e?void 0:e.closest(Dt);e&&(e.classList.add(z),e.disabled=!0),s&&s.classList.add(Y),fetch(theme.routes.cart_add_url,{method:"POST",headers:{"X-Requested-With":"XMLHttpRequest",Accept:"application/javascript"},body:t}).then((t=>t.json())).then((t=>{if(t.status)return this.addToCartError(t,e),void(e&&(e.classList.remove(z),e.disabled=!1));this.cart?(e&&(e.classList.remove(z),e.classList.add(N),e.dispatchEvent(new CustomEvent("theme:product:add",{detail:{response:t,button:e},bubbles:!0}))),"page"===theme.settings.cartType&&(window.location=theme.routes.cart_url),this.getCart()):window.location=theme.routes.cart_url})).catch((t=>{this.addToCartError(t,e),this.enableCartButtons()}))}updateCart(t={},e=null){this.cart.classList.add(z);let s=t.quantity;null!==e&&(s?e.classList.add(z):e.classList.add(X)),this.disableCartButtons();const i=this.cart.querySelector(`[${Jt}="${t.id}"]`)||e,o=(null==i?void 0:i.hasAttribute(Xt))?parseInt(i.getAttribute(Xt)):0,n=(null==i?void 0:i.hasAttribute(Qt))?i.getAttribute(Qt):null;if(0===o)return;const r={line:o,quantity:s};fetch(theme.routes.cart_change_url,{method:"post",headers:{"Content-Type":"application/json",Accept:"application/json"},body:JSON.stringify(r)}).then((t=>t.text())).then((t=>{if(JSON.parse(t).errors)return this.cartUpdateFailed=!0,this.updateErrorText(n),this.toggleErrorMessage(),this.resetLineItem(e),void this.enableCartButtons();this.getCart()})).catch((t=>{console.log(t),this.enableCartButtons()}))}resetLineItem(t){const e=t.querySelector(_t),s=e.getAttribute("value");e.value=s,t.classList.remove(z)}disableCartButtons(){const t=this.cart.querySelectorAll("input"),e=this.cart.querySelectorAll(`button, ${pt}`);t.length&&t.forEach((t=>{t.classList.add(R),t.blur(),t.disabled=!0})),e.length&&e.forEach((t=>{t.setAttribute(Vt,!0)}))}enableCartButtons(){const t=this.cart.querySelectorAll("input"),e=this.cart.querySelectorAll(`button, ${pt}`);t.length&&t.forEach((t=>{t.classList.remove(R),t.disabled=!1})),e.length&&e.forEach((t=>{t.removeAttribute(Vt)})),this.cart.classList.remove(z)}updateErrorText(t){this.cartErrorHolder.querySelector(Lt).innerText=t}toggleErrorMessage(){this.cartErrorHolder&&(this.cartErrorHolder.classList.toggle(K,this.cartUpdateFailed),this.cartUpdateFailed=!1)}cartErrorsHandler(t){return t.ok?t:t.json().then((function(e){throw new B({status:t.statusText,headers:t.headers,json:e})}))}addToCartError(t,e){var s;if(null!==e){const s=e.closest(Pt)||e.closest(Dt)||e.closest(Ft);let i=null==s?void 0:s.querySelector(St);const o=e.closest(Dt);if(o&&o.querySelector(St)&&(i=o.querySelector(St)),i){let e=`${t.message}: ${t.description}`;t.message==t.description&&(e=t.message),i.innerHTML=`<div class="errors">${e}<button type="button" class="errors__close" data-close-error><svg aria-hidden="true" focusable="false" role="presentation" width="24px" height="24px" stroke-width="1" viewBox="0 0 24 24" fill="none" xmlns="http://www.w3.org/2000/svg" color="currentColor" class="icon icon-cancel"><path d="M6.758 17.243L12.001 12m5.243-5.243L12 12m0 0L6.758 6.757M12.001 12l5.243 5.243" stroke="currentColor" stroke-width="1" stroke-linecap="round" stroke-linejoin="round"></path></svg></button></div>`,i.classList.add(Y),this.formErrorsEvents(i)}e.dispatchEvent(new CustomEvent("theme:product:add-error",{detail:{response:t,button:e},bubbles:!0}))}const i=null==e?void 0:e.closest(Dt);i&&i.dispatchEvent(new CustomEvent("theme:cart:error",{bubbles:!0,detail:{message:t.message,description:t.description,holder:i}})),null===(s=this.cart)||void 0===s||s.classList.remove(z)}productAddCallback(t){let e=[],s=null;const i="theme:product:add-error"==t.type,o=t.detail.button,n=document.querySelector(at);e.push(o),s=o.closest(Dt),n&&e.push(n),e.forEach((t=>{t.classList.remove(z),i||t.classList.add(N)})),setTimeout((()=>{e.forEach((t=>{var e,s;t.classList.remove(N);(null===(e=t.closest(At))||void 0===e?void 0:e.classList.contains(Z))||(null===(s=t.closest(At))||void 0===s?void 0:s.classList.contains(tt))||(t.disabled=!1)})),null==s||s.classList.remove(Y)}),1e3)}toggleClassesOnContainers(){const t=this.hasItemsInCart();this.cart.classList.toggle(U,!t),!t&&this.cartDrawer&&setTimeout((()=>{this.a11y.trapFocus(this.cartDrawer,{elementToFocus:this.cartDrawer.querySelector(ct)})}),100)}build(t){var e;const s=t.querySelector(st),i=t.querySelector(it),o=Boolean(null===s&&null===i),n=t.querySelector(ot),r=t.querySelector(ft);this.priceHolder&&n&&(this.priceHolder.innerHTML=n.innerHTML),o?(this.itemsHolder.innerHTML=t.innerHTML,this.upsellProductsHolder&&(this.upsellProductsHolder.innerHTML="")):(this.itemsHolder.innerHTML=s.innerHTML,this.upsellProductsHolder&&(this.upsellProductsHolder.innerHTML=i.innerHTML),this.skipUpsellProductEvent(),this.checkSkippedUpsellProductsFromStorage(),this.toggleCartUpsellWidgetVisibility()),this.newTotalItems=s&&s.querySelectorAll(Mt).length?s.querySelectorAll(Mt).length:0,this.subtotal=r&&r.hasAttribute(Ut)?parseInt(r.getAttribute(Ut)):0,this.cartCount=this.getCartItemCount(),document.dispatchEvent(new CustomEvent("theme:cart:change",{bubbles:!0,detail:{cartCount:this.cartCount}})),this.cartTotal.innerHTML=0===this.subtotal?window.theme.strings.free:window.theme.formatMoney(this.subtotal,theme.moneyWithCurrencyFormat),this.totalItems!==this.newTotalItems&&(this.totalItems=this.newTotalItems,this.toggleClassesOnContainers()),(null===(e=this.cartDrawer)||void 0===e?void 0:e.classList.contains(J))&&this.cart.classList.add(G),this.cart.classList.remove(z),this.hasItemsInCart()||this.cartEmpty.querySelectorAll(nt).forEach((t=>{t.classList.remove(W)})),this.freeShippingMessageHandle(this.subtotal),this.cartRemoveEvents(),this.cartUpdateEvents(),this.toggleErrorMessage(),this.enableCartButtons(),this.updateProgress(),this.animateItems(),document.dispatchEvent(new CustomEvent("theme:product:added",{bubbles:!0}))}getCartItemCount(){return Array.from(this.cart.querySelectorAll(_t)).reduce(((t,e)=>t+parseInt(e.value)),0)}hasItemsInCart(){return this.totalItems>0}freeShippingMessageHandle(t){this.freeShipping.length&&this.freeShipping.forEach((e=>{const s=e.hasAttribute(jt)&&"true"===e.getAttribute(jt)&&t>=0;e.classList.toggle(Q,s&&t>=this.freeShippingLimit)}))}updateProgress(){if(this.freeShipping=document.querySelectorAll(kt),!this.freeShipping.length)return;const t=isNaN(this.subtotal/this.freeShippingLimit)?100:this.subtotal/this.freeShippingLimit,e=Math.min(100*t,100),s=this.circumference-e/100*this.circumference/2,i=window.theme.formatMoney(this.freeShippingLimit-this.subtotal,theme.moneyFormat);this.freeShipping.forEach((t=>{const o=t.querySelector(qt),n=t.querySelector(Ct),r=t.querySelector(It);r&&(r.innerHTML=i.replace(".00","")),o&&(o.value=e),n&&n.style.setProperty("--stroke-dashoffset",`${s}`)}))}skipUpsellProductEvent(){if(null===this.upsellProductsHolder)return;const t=this.upsellProductsHolder.querySelectorAll(rt);t.length&&t.forEach((t=>{t.addEventListener("click",(e=>{e.preventDefault();const s=t.closest(Dt).getAttribute(Kt);this.skipUpsellProductsArray.includes(s)||this.skipUpsellProductsArray.push(s),window.sessionStorage.setItem("skip_upsell_products",this.skipUpsellProductsArray),this.removeUpsellProduct(s),this.toggleCartUpsellWidgetVisibility()}))}))}checkSkippedUpsellProductsFromStorage(){const t=window.sessionStorage.getItem("skip_upsell_products");if(!t)return;t.split(",").forEach((t=>{this.skipUpsellProductsArray.includes(t)||this.skipUpsellProductsArray.push(t),this.removeUpsellProduct(t)}))}removeUpsellProduct(t){if(!this.upsellProductsHolder)return;const e=this.upsellProductsHolder.querySelector(`[${Kt}="${t}"]`);e&&e.parentNode.remove()}toggleCartUpsellWidgetVisibility(){if(!this.upsellProductsHolder)return;const t=this.upsellProductsHolder.querySelectorAll(Dt),e=this.upsellProductsHolder.closest(Bt);if(e&&(e.classList.toggle(V,!t.length),t.length&&!e.hasAttribute(Yt)&&e.hasAttribute(Zt))){e.setAttribute(Yt,!0);const t=e.querySelector(Nt);t&&(t.style.height="auto")}}resetAnimatedItems(){this.cart.querySelectorAll(nt).forEach((t=>{t.classList.remove(W),t.classList.remove(j)}))}animateItems(t){requestAnimationFrame((()=>{let e=this.cart;t&&t.detail&&t.detail.target&&(e=t.detail.target),null==e||e.querySelectorAll(nt).forEach((t=>{t.classList.add(W)}))}))}constructor(){super(),this.a11y=window.theme.a11y}};customElements.get("cart-items")||customElements.define("cart-items",ee);const se="data-cart-count",ie="data-limit";let oe=class extends HTMLElement{connectedCallback(){document.addEventListener("theme:cart:change",this.onCartChangeCallback)}disconnectedCallback(){document.addEventListener("theme:cart:change",this.onCartChangeCallback)}onCartChange(t){this.cartCount=t.detail.cartCount,this.update()}update(){if(null!==this.cartCount){this.setAttribute(se,this.cartCount);let t=this.cartCount;this.limit&&this.cartCount>=this.limit&&(t="9+"),this.innerText=t}}constructor(){super(),this.cartCount=null,this.limit=this.getAttribute(ie),this.onCartChangeCallback=this.onCartChange.bind(this)}};customElements.get("cart-count")||customElements.define("cart-count",oe);const ne="is-open",re="is-closing",ae="drawer--duplicate",le="drawer-editor-error",de={cartDrawer:"cart-drawer",cartDrawerClose:"[data-cart-drawer-close]",cartDrawerSection:'[data-section-type="cart-drawer"]',cartDrawerInner:"[data-cart-drawer-inner]",shopifySection:".shopify-section"},ce="data-drawer-underlay";let he=class extends HTMLElement{connectedCallback(){const t=this.closest(de.shopifySection);if(window.theme.hasCartDrawer){if(!window.Shopify.designMode)return void t.remove();{const t=document.createElement("div");t.classList.add(le),t.innerText="Cart drawer section already exists.",this.querySelector(`.${le}`)||this.querySelector(de.cartDrawerInner).append(t),this.classList.add(ae)}}window.theme.hasCartDrawer=!0,this.addEventListener("theme:cart-drawer:show",this.openCartDrawer),document.addEventListener("theme:cart:toggle",this.toggleCartDrawer),document.addEventListener("theme:quick-add:open",this.closeCartDrawer),document.addEventListener("theme:product:added",this.openCartDrawerOnProductAdded),document.addEventListener("shopify:block:select",this.openCartDrawerOnSelect),document.addEventListener("shopify:section:select",this.openCartDrawerOnSelect),document.addEventListener("shopify:section:deselect",this.closeCartDrawerOnDeselect)}disconnectedCallback(){document.removeEventListener("theme:product:added",this.openCartDrawerOnProductAdded),document.removeEventListener("theme:cart:toggle",this.toggleCartDrawer),document.removeEventListener("theme:quick-add:open",this.closeCartDrawer),document.removeEventListener("shopify:block:select",this.openCartDrawerOnSelect),document.removeEventListener("shopify:section:select",this.openCartDrawerOnSelect),document.removeEventListener("shopify:section:deselect",this.closeCartDrawerOnDeselect),document.querySelectorAll(de.cartDrawer).length<=1&&(window.theme.hasCartDrawer=!1),l()}openCartDrawerOnProductAdded(){this.cartDrawerIsOpen||this.openCartDrawer()}openCartDrawerOnSelect(t){(t.target.querySelector(de.shopifySection)||t.target.closest(de.shopifySection)||t.target)===this.cartDrawerSection&&this.openCartDrawer(!0)}closeCartDrawerOnDeselect(){this.cartDrawerIsOpen&&this.closeCartDrawer()}openCartDrawer(t=!1){!t&&this.classList.contains(ae)||(this.cartDrawerIsOpen=!0,this.onBodyClickEvent=this.onBodyClickEvent||this.onBodyClick.bind(this),document.body.addEventListener("click",this.onBodyClickEvent),document.dispatchEvent(new CustomEvent("theme:cart-drawer:open",{detail:{target:this},bubbles:!0})),document.dispatchEvent(new CustomEvent("theme:scroll:lock",{bubbles:!0})),this.classList.add(ne),this.observeAdditionalCheckoutButtons(),window.theme.waitForAnimationEnd(this.cartDrawerInner).then((()=>{this.a11y.trapFocus(this,{elementToFocus:this.querySelector(de.cartDrawerClose)})})))}closeCartDrawer(){this.classList.contains(ne)&&(this.classList.add(re),this.classList.remove(ne),this.cartDrawerIsOpen=!1,document.dispatchEvent(new CustomEvent("theme:cart-drawer:close",{bubbles:!0})),this.a11y.removeTrapFocus(),this.a11y.autoFocusLastElement(),document.body.removeEventListener("click",this.onBodyClickEvent),document.dispatchEvent(new CustomEvent("theme:scroll:unlock",{bubbles:!0})),window.theme.waitForAnimationEnd(this.cartDrawerInner).then((()=>{this.classList.remove(re)})))}toggleCartDrawer(){this.cartDrawerIsOpen?this.closeCartDrawer():this.openCartDrawer()}closeCartEvents(){this.cartDrawerClose.addEventListener("click",(t=>{t.preventDefault(),this.closeCartDrawer()})),this.addEventListener("keyup",(t=>{"Escape"===t.code&&this.closeCartDrawer()}))}onBodyClick(t){t.target.hasAttribute(ce)&&this.closeCartDrawer()}observeAdditionalCheckoutButtons(){const t=this.querySelector(de.additionalCheckoutButtons);if(t){const e=new MutationObserver((()=>{this.a11y.trapFocus(this,{elementToFocus:this.querySelector(de.cartDrawerClose)}),e.disconnect()}));e.observe(t,{subtree:!0,childList:!0})}}constructor(){super(),this.cartDrawerIsOpen=!1,this.cartDrawerClose=this.querySelector(de.cartDrawerClose),this.cartDrawerInner=this.querySelector(de.cartDrawerInner),this.openCartDrawer=this.openCartDrawer.bind(this),this.closeCartDrawer=this.closeCartDrawer.bind(this),this.toggleCartDrawer=this.toggleCartDrawer.bind(this),this.openCartDrawerOnProductAdded=this.openCartDrawerOnProductAdded.bind(this),this.openCartDrawerOnSelect=this.openCartDrawerOnSelect.bind(this),this.closeCartDrawerOnDeselect=this.closeCartDrawerOnDeselect.bind(this),this.cartDrawerSection=this.closest(de.shopifySection),this.a11y=window.theme.a11y,this.closeCartEvents()}};customElements.get("cart-drawer")||customElements.define("cart-drawer",he);const ue="[data-collapsible]",pe="[data-collapsible-trigger]",me="[data-collapsible-body]",ve="[data-collapsible-content]",ge="desktop",be="disabled",we="mobile",fe="open",ye="single";let Le=class extends HTMLElement{connectedCallback(){this.toggle(),document.addEventListener("theme:resize:width",this.toggle),this.collapsibles.forEach((t=>{const e=t.querySelector(pe),s=t.querySelector(me);null==e||e.addEventListener("click",(t=>this.onCollapsibleClick(t))),null==s||s.addEventListener("transitionend",(e=>{e.target===s&&("true"==t.getAttribute(fe)&&this.setBodyHeight(s,"auto"),"false"==t.getAttribute(fe)&&(t.removeAttribute(fe),this.setBodyHeight(s,"")))}))}))}disconnectedCallback(){document.removeEventListener("theme:resize:width",this.toggle)}toggle(){const t=!window.theme.isMobile();this.collapsibles.forEach((e=>{if(!e.hasAttribute(ge)&&!e.hasAttribute(we))return;const s=e.hasAttribute(ge)?e.getAttribute(ge):"true",i=e.hasAttribute(we)?e.getAttribute(we):"true",o=t&&"true"==s||!t&&"true"==i,n=e.querySelector(me);o?(e.removeAttribute(be),e.querySelector(pe).removeAttribute("tabindex"),e.removeAttribute(fe),this.setBodyHeight(n,"")):(e.setAttribute(be,""),e.setAttribute("open",!0),e.querySelector(pe).setAttribute("tabindex",-1))}))}open(t){if("true"==t.getAttribute("open"))return;const e=t.querySelector(me),s=t.querySelector(ve);t.setAttribute("open",!0),this.setBodyHeight(e,s.offsetHeight)}close(t){if(!t.hasAttribute("open"))return;const e=t.querySelector(me),s=t.querySelector(ve);this.setBodyHeight(e,s.offsetHeight),t.setAttribute("open",!1),setTimeout((()=>{requestAnimationFrame((()=>{this.setBodyHeight(e,0)}))}))}setBodyHeight(t,e){t.style.height="auto"!==e&&""!==e?`${e}px`:e}onCollapsibleClick(t){t.preventDefault();const e=t.target.closest(ue);this.single&&this.collapsibles.forEach((t=>{t.hasAttribute(fe)&&t!=e&&requestAnimationFrame((()=>{this.close(t)}))})),e.hasAttribute(fe)?this.close(e):this.open(e),e.dispatchEvent(new CustomEvent("theme:form:sticky",{bubbles:!0,detail:{element:"accordion"}})),e.dispatchEvent(new CustomEvent("theme:collapsible:toggle",{bubbles:!0}))}constructor(){super(),this.collapsibles=this.querySelectorAll(ue),this.single=this.hasAttribute(ye),this.toggle=this.toggle.bind(this)}};customElements.get("collapsible-elements")||customElements.define("collapsible-elements",Le);const Ee="[data-deferred-media-button]",Se="video, model-viewer, iframe",Ae='[data-host="youtube"]',ke='[data-host="vimeo"]',Ce="template",qe="video",Te="product-model",Me="loaded",xe="autoplay";let Ie=class extends HTMLElement{loadContent(t=!0){if(this.pauseAllMedia(),!this.getAttribute(Me)){const e=document.createElement("div"),s=this.querySelector(Ce).content.firstElementChild.cloneNode(!0);e.appendChild(s),this.setAttribute(Me,!0);const i=this.appendChild(e.querySelector(Se));t&&i.focus(),"VIDEO"==i.nodeName&&i.getAttribute(xe)&&i.play()}}pauseAllMedia(){document.querySelectorAll(Ae).forEach((t=>{t.contentWindow.postMessage('{"event":"command","func":"pauseVideo","args":""}',"*")})),document.querySelectorAll(ke).forEach((t=>{t.contentWindow.postMessage('{"method":"pause"}',"*")})),document.querySelectorAll(qe).forEach((t=>t.pause())),document.querySelectorAll(Te).forEach((t=>{t.modelViewerUI&&t.modelViewerUI.pause()}))}constructor(){super();const t=this.querySelector(Ee);null==t||t.addEventListener("click",this.loadContent.bind(this))}};customElements.get("deferred-media")||customElements.define("deferred-media",Ie),window.theme.DeferredMedia=window.theme.DeferredMedia||Ie;const Oe="is-visible";class Pe{init(){var t;const e={root:this.container,threshold:[.01,.5,.75,.99]};this.observer=new IntersectionObserver((t=>{t.forEach((t=>{t.intersectionRatio>=.99?t.target.classList.add(Oe):t.target.classList.remove(Oe)}))}),e),null===(t=this.container.querySelectorAll(this.itemSelector))||void 0===t||t.forEach((t=>{this.observer.observe(t)}))}destroy(){this.observer.disconnect()}constructor(t,e){t&&e&&(this.observer=null,this.container=t,this.itemSelector=e,this.init())}}const He="is-dragging",De="is-enabled",Fe="is-scrolling",_e="is-visible",$e="[data-grid-item]";class Be{handleMouseDown(t){t.preventDefault(),this.isDown=!0,this.startX=t.pageX-this.slider.offsetLeft,this.scrollLeft=this.slider.scrollLeft,this.cancelMomentumTracking()}handleMouseLeave(){this.isDown&&(this.isDown=!1,this.beginMomentumTracking())}handleMouseUp(){this.isDown=!1,this.beginMomentumTracking()}handleMouseMove(t){if(!this.isDown)return;t.preventDefault();const e=1*(t.pageX-this.slider.offsetLeft-this.startX),s=this.slider.scrollLeft,i=e>0?1:-1;this.slider.classList.add(He,Fe),this.slider.scrollLeft=this.scrollLeft-e,this.slider.scrollLeft!==s&&(this.velX=this.slider.scrollLeft-s||i)}handleMouseWheel(){this.cancelMomentumTracking(),this.slider.classList.remove(Fe)}beginMomentumTracking(){this.isScrolling=!1,this.slider.classList.remove(He),this.cancelMomentumTracking(),this.scrollToSlide()}cancelMomentumTracking(){cancelAnimationFrame(this.scrollAnimation)}scrollToSlide(){if(!this.velX&&!this.isScrolling)return;const t=this.slider.querySelector(`${$e}.${_e}`);if(!t)return;const e=parseInt(window.getComputedStyle(t).marginRight)||0,s=t.offsetWidth+e,i=t.offsetLeft,o=this.velX>0?1:-1,n=Math.floor(Math.abs(this.velX)/100)||1;this.startPosition=this.slider.scrollLeft,this.distance=i-this.startPosition,this.startTime=performance.now(),this.isScrolling=!0,o<0&&this.velX<s&&(this.distance-=s*n),o>0&&this.velX<s&&(this.distance+=s*n),this.scrollAnimation=requestAnimationFrame(this.scrollStep)}scrollStep(){const t=performance.now()-this.startTime,e=parseFloat(this.easeOutCubic(Math.min(t,this.duration))).toFixed(1);this.slider.scrollLeft=e,t<this.duration?this.scrollAnimation=requestAnimationFrame(this.scrollStep):(this.slider.classList.remove(Fe),this.velX=0,this.isScrolling=!1)}easeOutCubic(t){return t/=this.duration,t--,this.distance*(t*t*t+1)+this.startPosition}destroy(){this.slider.classList.remove(De),this.slider.removeEventListener("mousedown",this.handleMouseDown),this.slider.removeEventListener("mouseleave",this.handleMouseLeave),this.slider.removeEventListener("mouseup",this.handleMouseUp),this.slider.removeEventListener("mousemove",this.handleMouseMove),this.slider.removeEventListener("wheel",this.handleMouseWheel)}constructor(t){this.slider=t,this.isDown=!1,this.startX=0,this.scrollLeft=0,this.velX=0,this.scrollAnimation=null,this.isScrolling=!1,this.duration=800,this.scrollStep=this.scrollStep.bind(this),this.scrollToSlide=this.scrollToSlide.bind(this),this.handleMouseDown=this.handleMouseDown.bind(this),this.handleMouseLeave=this.handleMouseLeave.bind(this),this.handleMouseUp=this.handleMouseUp.bind(this),this.handleMouseMove=this.handleMouseMove.bind(this),this.handleMouseWheel=this.handleMouseWheel.bind(this),this.slider.addEventListener("mousedown",this.handleMouseDown),this.slider.addEventListener("mouseleave",this.handleMouseLeave),this.slider.addEventListener("mouseup",this.handleMouseUp),this.slider.addEventListener("mousemove",this.handleMouseMove),this.slider.addEventListener("wheel",this.handleMouseWheel,{passive:!0}),this.slider.classList.add(De)}}const We="[data-button-arrow]",Ne="[data-collection-image]",Re="[data-column-image]",Ue="[data-product-image]",Ve="[data-grid-item]",je="[data-grid-slider]",ze="data-button-prev",Je="data-button-next",Xe="align-arrows",Qe="slider__arrows",Ye="is-visible",Ke="scroll-snap-disabled";customElements.get("grid-slider")||customElements.define("grid-slider",class extends HTMLElement{connectedCallback(){this.init(),this.addEventListener("theme:grid-slider:init",this.init)}init(){this.slider=this.querySelector(je),this.slides=this.querySelectorAll(Ve),this.buttons=this.querySelectorAll(We),this.slider.classList.add(Ke),this.toggleSlider(),document.addEventListener("theme:resize:width",this.toggleSlider),window.theme.waitForAllAnimationsEnd(this).then((()=>{this.slider.classList.remove(Ke)})).catch((()=>{this.slider.classList.remove(Ke)}))}toggleSlider(){if(!(this.slider.clientWidth<this.getSlidesWidth())||window.theme.isMobile()&&window.theme.touch)this.destroy();else{if(this.isInitialized)return;this.slidesObserver=new Pe(this.slider,Ve),this.initArrows(),this.isInitialized=!0,this.draggableSlider=new Be(this.slider)}}initArrows(){if(!this.buttons.length){const t=document.createElement("div");t.classList.add(Qe),t.innerHTML=theme.sliderArrows.prev+theme.sliderArrows.next,this.append(t),this.buttons=this.querySelectorAll(We),this.buttonPrev=this.querySelector(`[${ze}]`),this.buttonNext=this.querySelector(`[${Je}]`)}this.toggleArrowsObserver(),this.hasAttribute(Xe)&&(this.positionArrows(),this.arrowsResizeObserver()),this.buttons.forEach((t=>{t.addEventListener("click",this.onButtonArrowClick)}))}buttonArrowClickEvent(t){t.preventDefault();const e=this.slider.querySelector(`${Ve}.${Ye}`);let s=null;t.target.hasAttribute(ze)&&(s=null==e?void 0:e.previousElementSibling),t.target.hasAttribute(Je)&&(s=null==e?void 0:e.nextElementSibling),this.goToSlide(s)}removeArrows(){var t;null===(t=this.querySelector(`.${Qe}`))||void 0===t||t.remove()}goToSlide(t){t&&this.slider.scrollTo({top:0,left:t.offsetLeft,behavior:"smooth"})}getSlidesWidth(){var t;return(null===(t=this.slider.querySelector(Ve))||void 0===t?void 0:t.clientWidth)*this.slider.querySelectorAll(Ve).length}toggleArrowsObserver(){if(this.buttonPrev&&this.buttonNext){const t=this.slides.length,e=this.slides[0],s=this.slides[t-1],i={attributes:!0,childList:!1,subtree:!1},o=t=>{for(const i of t)if("attributes"===i.type){const t=i.target,o=Boolean(t.classList.contains(Ye));t==e&&(this.buttonPrev.disabled=o),t==s&&(this.buttonNext.disabled=o)}};e&&s&&(this.firstLastSlidesObserver=new MutationObserver(o),this.firstLastSlidesObserver.observe(e,i),this.firstLastSlidesObserver.observe(s,i))}}positionArrows(){const t=this.slider.querySelector(Ue)||this.slider.querySelector(Ne)||this.slider.querySelector(Re)||this.slider;t&&this.style.setProperty("--button-position",t.clientHeight/2+"px")}arrowsResizeObserver(){document.addEventListener("theme:resize:width",this.positionArrows)}disconnectedCallback(){this.destroy(),document.removeEventListener("theme:resize:width",this.toggleSlider)}destroy(){var t,e;this.isInitialized=!1,null===(t=this.draggableSlider)||void 0===t||t.destroy(),this.draggableSlider=null,null===(e=this.slidesObserver)||void 0===e||e.destroy(),this.slidesObserver=null,this.removeArrows(),document.removeEventListener("theme:resize:width",this.positionArrows)}constructor(){super(),this.isInitialized=!1,this.draggableSlider=null,this.positionArrows=this.positionArrows.bind(this),this.onButtonArrowClick=t=>this.buttonArrowClickEvent(t),this.slidesObserver=null,this.firstLastSlidesObserver=null,this.isDragging=!1,this.toggleSlider=this.toggleSlider.bind(this)}}),window.theme.hasOpenModals=function(){const t=Boolean(document.querySelectorAll("dialog[open][data-scroll-lock-required]").length),e=Boolean(document.querySelectorAll(".drawer.is-open").length);return t||e};const Ge="cart-drawer",Ze="[data-cart-toggle]",ts='.navlink[href="#"]',es="[data-header-desktop]",ss=".main-content > .shopify-section.section-overlay-header:first-of-type",is=".page-header",os="[data-prevent-transparent-header]",ns="[data-child-takes-space]",rs="[data-takes-space-wrapper]",as="js__header__clone",ls="has-first-section-overlay-header",ds="shopify-section-group-header-group",cs="js__show__mobile",hs="has-header-sticky",us="js__header__stuck",ps="has-header-transparent",ms="header-wrapper",vs="data-drawer",gs="data-drawer-toggle",bs="data-scroll-locked",ws="data-header-sticky",fs="data-header-transparent";customElements.get("header-component")||customElements.define("header-component",class extends HTMLElement{connectedCallback(){this.killDeadLinks(),this.drawerToggleEvent(),this.cartToggleEvent(),this.initSticky(),"drawer"!==this.style&&this.desktop&&(this.minWidth=this.getMinWidth(),this.listenWidth())}listenWidth(){"ResizeObserver"in window?(this.resizeObserver=new ResizeObserver(this.checkWidth),this.resizeObserver.observe(this)):document.addEventListener("theme:resize",this.checkWidth)}drawerToggleEvent(){var t;null===(t=this.querySelectorAll(`[${gs}]`))||void 0===t||t.forEach((t=>{t.addEventListener("click",(()=>{let e;const s=t.hasAttribute(gs)?t.getAttribute(gs):"",i=document.querySelector(`[${vs}="${s}"]`),o=document.querySelector(`mobile-menu > [${vs}]`);e=!window.theme.isMobile()?i:"new"===theme.settings.mobileMenuType&&o||i,e.dispatchEvent(new CustomEvent("theme:drawer:toggle",{bubbles:!1,detail:{button:t}}))}))}))}killDeadLinks(){this.deadLinks.forEach((t=>{t.onclick=t=>{t.preventDefault()}}))}checkWidth(){if(document.body.clientWidth<this.minWidth){this.classList.add(cs);const{headerHeight:t}=window.theme.readHeights();document.documentElement.style.setProperty("--header-height",`${t}px`)}else this.classList.remove(cs)}getMinWidth(){const t=document.createElement("div");t.classList.add(as,ms),t.appendChild(this.querySelector("header").cloneNode(!0)),document.body.appendChild(t);const e=t.querySelectorAll(rs);let s=0,i=0;return e.forEach((t=>{const e=t.querySelectorAll(ns);let o=0;o=3===e.length?this._sumSplitWidths(e):this._sumWidths(e),o>s&&(s=o,i=20*e.length)})),document.body.removeChild(t),s+i}cartToggleEvent(){var t;"drawer"===theme.settings.cartType&&(null===(t=this.querySelectorAll(Ze))||void 0===t||t.forEach((t=>{t.addEventListener("click",(e=>{const s=document.querySelector(Ge);s&&(e.preventDefault(),s.dispatchEvent(new CustomEvent("theme:cart-drawer:show")),window.a11y.lastElement=t)}))})))}toggleButtonClick(t){t.preventDefault(),document.dispatchEvent(new CustomEvent("theme:cart:toggle",{bubbles:!0}))}initSticky(){var t;this.isSticky&&(this.isStuck=!1,this.cls=this.classList,this.headerOffset=null===(t=document.querySelector(is))||void 0===t?void 0:t.offsetTop,this.updateHeaderOffset=this.updateHeaderOffset.bind(this),this.scrollEvent=t=>this.onScroll(t),this.listen(),this.stickOnLoad())}listen(){document.addEventListener("theme:scroll",this.scrollEvent),document.addEventListener("shopify:section:load",this.updateHeaderOffset),document.addEventListener("shopify:section:unload",this.updateHeaderOffset)}onScroll(t){t.detail.down?!this.isStuck&&t.detail.position>this.headerOffset&&this.stickSimple():t.detail.position<=this.headerOffset&&this.unstickSimple()}updateHeaderOffset(t){t.target.classList.contains(ds)&&setTimeout((()=>{var t;this.headerOffset=null===(t=document.querySelector(is))||void 0===t?void 0:t.offsetTop}))}stickOnLoad(){window.scrollY>this.headerOffset&&this.stickSimple()}stickSimple(){this.cls.add(us),this.isStuck=!0}unstickSimple(){document.documentElement.hasAttribute(bs)||(this.cls.remove(us),this.isStuck=!1)}_sumSplitWidths(t){let e=[];t.forEach((t=>{t.firstElementChild&&e.push(t.firstElementChild.clientWidth)})),e[0]>e[2]?e[2]=e[0]:e[0]=e[2];return e.reduce(((t,e)=>t+e))}_sumWidths(t){let e=0;return t.forEach((t=>{e+=t.clientWidth})),e}disconnectedCallback(){var t;"ResizeObserver"in window?null===(t=this.resizeObserver)||void 0===t||t.unobserve(this):document.removeEventListener("theme:resize",this.checkWidth);this.isSticky&&(document.removeEventListener("theme:scroll",this.scrollEvent),document.removeEventListener("shopify:section:load",this.updateHeaderOffset),document.removeEventListener("shopify:section:unload",this.updateHeaderOffset))}constructor(){super(),this.style=this.dataset.style,this.desktop=this.querySelector(es),this.deadLinks=document.querySelectorAll(ts),this.resizeObserver=null,this.checkWidth=this.checkWidth.bind(this),this.isSticky=this.hasAttribute(ws),document.body.classList.toggle(hs,this.isSticky);let t=!1;const e=document.querySelector(ss);e&&!e.querySelector(os)&&(t=!0),document.body.classList.toggle(ps,this.hasAttribute(fs)),document.body.classList.toggle(ls,t)}});const ys="[data-top-link]",Ls="[data-header-wrapper]",Es="[data-stagger]",Ss="[data-stagger-first]",As="[data-stagger-second]",ks="is-visible",Cs="meganav--visible",qs="meganav--is-transitioning";customElements.get("hover-disclosure")||customElements.define("hover-disclosure",class extends HTMLElement{connectedCallback(){this.setAttribute("aria-haspopup",!0),this.setAttribute("aria-expanded",!1),this.setAttribute("aria-controls",this.key),this.connectHoverToggle(),this.handleTablets(),this.staggerChildAnimations(),this.addEventListener("theme:disclosure:show",(t=>{this.showDisclosure(t)})),this.addEventListener("theme:disclosure:hide",(t=>{this.hideDisclosure(t)}))}showDisclosure(t){t&&t.type&&"mouseenter"===t.type&&this.wrapper.classList.add(qs),this.grandparent?this.wrapper.classList.add(Cs):this.wrapper.classList.remove(Cs),this.setAttribute("aria-expanded",!0),this.classList.add(ks),this.disclosure.classList.add(ks),this.transitionTimeout&&clearTimeout(this.transitionTimeout),this.transitionTimeout=setTimeout((()=>{this.wrapper.classList.remove(qs)}),200)}hideDisclosure(){this.classList.remove(ks),this.disclosure.classList.remove(ks),this.setAttribute("aria-expanded",!1),this.wrapper.classList.remove(Cs,qs)}staggerChildAnimations(){const t=this.querySelectorAll(Es);let e=50;t.forEach(((t,s)=>{t.style.transitionDelay=s*e+10+"ms",e*=.95}));this.querySelectorAll(Ss).forEach(((t,e)=>{const s=100*e;t.style.transitionDelay=`${s}ms`,t.parentElement.querySelectorAll(As).forEach(((t,e)=>{const i=20*(e+1);t.style.transitionDelay=`${s+i}ms`}))}))}handleTablets(){this.addEventListener("touchstart",function(t){this.classList.contains(ks)||(t.preventDefault(),this.showDisclosure(t))}.bind(this),{passive:!0})}connectHoverToggle(){this.addEventListener("mouseenter",(t=>this.showDisclosure(t))),this.link.addEventListener("focus",(t=>this.showDisclosure(t))),this.addEventListener("mouseleave",(()=>this.hideDisclosure())),this.addEventListener("focusout",(t=>{this.contains(t.relatedTarget)||this.hideDisclosure()})),this.addEventListener("keyup",(t=>{"Escape"===t.code&&this.hideDisclosure()}))}constructor(){super(),this.wrapper=this.closest(Ls),this.key=this.getAttribute("aria-controls"),this.link=this.querySelector(ys),this.grandparent=this.classList.contains("grandparent"),this.disclosure=document.getElementById(this.key),this.transitionTimeout=0}});const Ts="[data-drawer-inner]",Ms="[data-drawer-close]",xs="[data-drawer-underlay]",Is="[data-stagger-animation]",Os='button, [href], select, textarea, [tabindex]:not([tabindex="-1"])',Ps="drawer--animated",Hs="is-open",Ds="is-closing",Fs="is-focused";customElements.get("header-drawer")||customElements.define("header-drawer",class extends HTMLElement{connectDrawer(){this.addEventListener("theme:drawer:toggle",(t=>{var e;this.triggerButton=null===(e=t.detail)||void 0===e?void 0:e.button,this.classList.contains(Hs)?this.dispatchEvent(new CustomEvent("theme:drawer:close",{bubbles:!0})):this.dispatchEvent(new CustomEvent("theme:drawer:open",{bubbles:!0}))})),this.addEventListener("theme:drawer:close",this.hideDrawer),this.addEventListener("theme:drawer:open",this.showDrawer),document.addEventListener("theme:cart-drawer:open",this.hideDrawer)}closers(){var t;null===(t=this.querySelectorAll(Ms))||void 0===t||t.forEach((t=>{t.addEventListener("click",(()=>{this.hideDrawer()}))})),document.addEventListener("keyup",(t=>{"Escape"===t.code&&this.hideDrawer()})),this.underlay.addEventListener("click",(()=>{this.hideDrawer()}))}showDrawer(){var t;this.isAnimating||(this.isAnimating=!0,null===(t=this.triggerButton)||void 0===t||t.setAttribute("aria-expanded",!0),this.classList.add(Hs,Ps),document.dispatchEvent(new CustomEvent("theme:scroll:lock",{bubbles:!0})),this.drawerInner&&(this.a11y.removeTrapFocus(),window.theme.waitForAnimationEnd(this.drawerInner).then((()=>{this.isAnimating=!1,this.a11y.trapFocus(this.drawerInner,{elementToFocus:this.querySelector(Os)})}))))}hideDrawer(){!this.isAnimating&&this.classList.contains(Hs)&&(this.isAnimating=!0,this.classList.add(Ds),this.classList.remove(Hs),this.a11y.removeTrapFocus(),this.triggerButton&&(this.triggerButton.setAttribute("aria-expanded",!1),document.body.classList.contains(Fs)&&this.triggerButton.focus()),document.dispatchEvent(new CustomEvent("theme:scroll:unlock",{bubbles:!0})),window.theme.waitForAnimationEnd(this.drawerInner).then((()=>{this.classList.remove(Ds,Ps),this.isAnimating=!1,document.dispatchEvent(new CustomEvent("theme:sliderule:close",{bubbles:!1}))})))}disconnectedCallback(){document.removeEventListener("theme:cart-drawer:open",this.hideDrawer)}constructor(){super(),this.a11y=window.theme.a11y,this.isAnimating=!1,this.drawer=this,this.drawerInner=this.querySelector(Ts),this.underlay=this.querySelector(xs),this.triggerButton=null,this.staggers=this.querySelectorAll(Is),this.showDrawer=this.showDrawer.bind(this),this.hideDrawer=this.hideDrawer.bind(this),this.connectDrawer(),this.closers()}});const _s={animates:"data-animates",sliderule:"[data-sliderule]",slideruleOpen:"data-sliderule-open",slideruleClose:"data-sliderule-close",sliderulePane:"data-sliderule-pane",drawerContent:"[data-drawer-content]",focusable:'button, [href], input, select, textarea, [tabindex]:not([tabindex="-1"])',children:":scope > [data-animates],\n             :scope > * > [data-animates],\n             :scope > * > * > [data-animates],\n             :scope > * > .sliderule-grid  > *"},$s="is-visible",Bs="is-hiding",Ws="is-hidden",Ns="is-focused",Rs="is-scrolling";customElements.get("mobile-sliderule")||customElements.define("mobile-sliderule",class extends HTMLElement{clickEvents(){this.trigger.addEventListener("click",(()=>{this.cachedButton=this.trigger,this.showSliderule()})),this.exit.forEach((t=>{t.addEventListener("click",(()=>{this.hideSliderule()}))}))}keyboardEvents(){this.addEventListener("keyup",(t=>{t.stopPropagation(),"Escape"===t.code&&this.hideSliderule()}))}trapFocusSliderule(t=!0){const e=t?this.querySelector(this.exitSelector):this.cachedButton;this.a11y.removeTrapFocus(),e&&this.drawerContent&&this.a11y.trapFocus(this.drawerContent,{elementToFocus:document.body.classList.contains(Ns)?e:null})}hideSliderule(t=!1){const e=parseInt(this.pane.dataset.sliderulePane,10)-1;this.pane.setAttribute(_s.sliderulePane,e),this.pane.classList.add(Bs),this.sliderule.classList.add(Bs);const s=t?`[${_s.animates}].${Ws}`:`[${_s.animates}="${e}"]`,i=this.pane.querySelectorAll(s);i.length&&i.forEach((t=>{t.classList.remove(Ws)}));const o=t?this.pane.querySelectorAll(`.${$s}, .${Bs}`):this.childrenElements;o.forEach(((s,i)=>{const n=o.length-1==i;s.classList.remove($s),t&&(s.classList.remove(Bs),this.pane.classList.remove(Bs));const r=()=>{parseInt(this.pane.getAttribute(_s.sliderulePane))===e&&this.sliderule.classList.remove($s),this.sliderule.classList.remove(Bs),this.pane.classList.remove(Bs),n&&(this.a11y.removeTrapFocus(),t||this.trapFocusSliderule(!1)),s.removeEventListener("animationend",r)};window.theme.settings.enableAnimations?s.addEventListener("animationend",r):r()}))}showSliderule(){let t=null;const e=this.closest(`.${$s}`);let s=this.pane;e&&(s=e),s.scrollTo({top:0,left:0,behavior:"smooth"}),s.classList.add(Rs);const i=()=>{s.scrollTop<=0?(s.classList.remove(Rs),t&&cancelAnimationFrame(t)):t=requestAnimationFrame(i)};t=requestAnimationFrame(i);const o=parseInt(this.pane.dataset.sliderulePane,10),n=o+1;this.sliderule.classList.add($s),this.pane.setAttribute(_s.sliderulePane,n);const r=this.pane.querySelectorAll(`[${_s.animates}="${o}"]`);r.length&&r.forEach(((t,e)=>{const s=r.length-1==e;t.classList.add(Bs);const i=()=>{t.classList.remove(Bs),parseInt(this.pane.getAttribute(_s.sliderulePane))!==o&&t.classList.add(Ws),s&&this.trapFocusSliderule(),t.removeEventListener("animationend",i)};window.theme.settings.enableAnimations?t.addEventListener("animationend",i):i()}))}closeSliderule(){this.pane&&this.pane.hasAttribute(_s.sliderulePane)&&parseInt(this.pane.getAttribute(_s.sliderulePane))>0&&(this.hideSliderule(!0),parseInt(this.pane.getAttribute(_s.sliderulePane))>0&&this.pane.setAttribute(_s.sliderulePane,0))}disconnectedCallback(){document.removeEventListener("theme:sliderule:close",this.closeSliderule)}constructor(){super(),this.key=this.id,this.sliderule=this.querySelector(_s.sliderule);const t=`[${_s.slideruleOpen}='${this.key}']`;this.exitSelector=`[${_s.slideruleClose}='${this.key}']`,this.trigger=this.querySelector(t),this.exit=document.querySelectorAll(this.exitSelector),this.pane=this.trigger.closest(`[${_s.sliderulePane}]`),this.childrenElements=this.querySelectorAll(_s.children),this.drawerContent=this.closest(_s.drawerContent),this.cachedButton=null,this.a11y=window.theme.a11y,this.trigger.setAttribute("aria-haspopup",!0),this.trigger.setAttribute("aria-expanded",!1),this.trigger.setAttribute("aria-controls",this.key),this.closeSliderule=this.closeSliderule.bind(this),this.clickEvents(),this.keyboardEvents(),document.addEventListener("theme:sliderule:close",this.closeSliderule)}});const Us="details",Vs="[data-popdown]",js="[data-popdown-close]",zs='input:not([type="hidden"])',Js="mobile-menu",Xs="data-popdown-underlay",Qs="data-scroll-locked",Ys="is-open";let Ks=class extends HTMLElement{connectedCallback(){this.popdown.addEventListener("transitionend",this.popdownTransitionCallback),this.popdownContainer.addEventListener("keyup",(t=>"ESCAPE"===t.code.toUpperCase()&&this.close())),this.popdownContainer.addEventListener("toggle",this.detailsToggleCallback),this.popdownClose.addEventListener("click",this.close.bind(this))}detailsToggleCallback(t){t.target.hasAttribute("open")&&this.open()}popdownTransitionCallback(t){t.target===this.popdown&&(this.classList.contains(Ys)?"transform"!==t.propertyName&&"opacity"!==t.propertyName||this.a11y.trapFocus(this.popdown,{elementToFocus:this.popdown.querySelector(zs)}):(this.popdownContainer.removeAttribute("open"),this.a11y.removeTrapFocus()))}onBodyClick(t){this.contains(t.target)&&!t.target.hasAttribute(Xs)||this.close()}open(){var t;this.onBodyClickEvent=this.onBodyClickEvent||this.onBodyClick.bind(this),document.body.addEventListener("click",this.onBodyClickEvent),null===(t=this.mobileMenu)||void 0===t||t.dispatchEvent(new CustomEvent("theme:search:open")),document.documentElement.hasAttribute(Qs)||document.dispatchEvent(new CustomEvent("theme:scroll:lock",{bubbles:!0})),requestAnimationFrame((()=>{this.classList.add(Ys)}))}close(){var t;this.classList.remove(Ys),null===(t=this.mobileMenu)||void 0===t||t.dispatchEvent(new CustomEvent("theme:search:close")),document.body.removeEventListener("click",this.onBodyClickEvent),this.mobileMenu||document.dispatchEvent(new CustomEvent("theme:scroll:unlock",{bubbles:!0}))}constructor(){super(),this.popdown=this.querySelector(Vs),this.popdownContainer=this.querySelector(Us),this.popdownClose=this.querySelector(js),this.popdownTransitionCallback=this.popdownTransitionCallback.bind(this),this.detailsToggleCallback=this.detailsToggleCallback.bind(this),this.mobileMenu=this.closest(Js),this.a11y=window.theme.a11y}};customElements.get("header-search-popdown")||customElements.define("header-search-popdown",Ks);const Gs='input[type="search"]',Zs='[aria-selected="true"] a',ti='button[type="reset"]',ei="hidden";let si=class extends HTMLElement{toggleResetButton(){const t=this.resetButton.classList.contains(ei);this.input.value.length>0&&t?this.resetButton.classList.remove(ei):0!==this.input.value.length||t||this.resetButton.classList.add(ei)}onChange(){this.toggleResetButton()}shouldResetForm(){return!document.querySelector(Zs)}onFormReset(t){t.preventDefault(),this.shouldResetForm()&&(this.input.value="",this.toggleResetButton(),t.target.querySelector(Gs).focus())}constructor(){super(),this.input=this.querySelector(Gs),this.resetButton=this.querySelector(ti),this.input&&(this.input.form.addEventListener("reset",this.onFormReset.bind(this)),this.input.addEventListener("input",window.theme.debounce((t=>{this.onChange(t)}),300).bind(this)))}};customElements.define("header-search-form",si);const ii='input[type="search"]';let oi=class extends si{setupEventListeners(){let t=[];this.allSearchInputs.forEach((e=>t.push(e.form))),this.input.addEventListener("focus",this.onInputFocus.bind(this)),t.length<2||(t.forEach((t=>t.addEventListener("reset",this.onFormReset.bind(this)))),this.allSearchInputs.forEach((t=>t.addEventListener("input",this.onInput.bind(this)))))}onFormReset(t){super.onFormReset(t),super.shouldResetForm()&&this.keepInSync("",this.input)}onInput(t){const e=t.target;this.keepInSync(e.value,e)}onInputFocus(){window.theme.isMobile()&&this.scrollIntoView({behavior:"smooth"})}keepInSync(t,e){this.allSearchInputs.forEach((s=>{s!==e&&(s.value=t)}))}constructor(){super(),this.allSearchInputs=document.querySelectorAll(ii),this.setupEventListeners()}};customElements.get("main-search")||customElements.define("main-search",oi);const ni="[data-scrollbar]",ri="[data-scrollbar-arrow-prev]",ai="[data-scrollbar-arrow-next]",li="is-hidden",di="data-scrollbar-slider",ci="data-scrollbar-slide-fullwidth";customElements.get("native-scrollbar")||customElements.define("native-scrollbar",class extends HTMLElement{connectedCallback(){document.addEventListener("theme:resize",this.toggleNextArrow),this.scrollbar.hasAttribute(di)&&this.scrollToVisibleElement(),this.arrowNext&&this.arrowPrev&&this.events()}disconnectedCallback(){document.removeEventListener("theme:resize",this.toggleNextArrow)}events(){this.arrowNext.addEventListener("click",(t=>{t.preventDefault(),this.goToNext()})),this.arrowPrev.addEventListener("click",(t=>{t.preventDefault(),this.goToPrev()})),this.scrollbar.addEventListener("scroll",(()=>{this.togglePrevArrow(),this.toggleNextArrow()}))}goToNext(){const t=(this.scrollbar.hasAttribute(ci)?this.scrollbar.getBoundingClientRect().width:this.scrollbar.getBoundingClientRect().width/2)+this.scrollbar.scrollLeft;this.move(t),this.arrowPrev.classList.remove(li),this.toggleNextArrow()}goToPrev(){const t=this.scrollbar.hasAttribute(ci)?this.scrollbar.getBoundingClientRect().width:this.scrollbar.getBoundingClientRect().width/2,e=this.scrollbar.scrollLeft-t;this.move(e),this.arrowNext.classList.remove(li),this.togglePrevArrow()}toggleNextArrow(){requestAnimationFrame((()=>{var t;null===(t=this.arrowNext)||void 0===t||t.classList.toggle(li,Math.round(this.scrollbar.scrollLeft+this.scrollbar.getBoundingClientRect().width+1)>=this.scrollbar.scrollWidth)}))}togglePrevArrow(){requestAnimationFrame((()=>{this.arrowPrev.classList.toggle(li,this.scrollbar.scrollLeft<=0)}))}scrollToVisibleElement(){[].forEach.call(this.scrollbar.children,(t=>{t.addEventListener("click",(e=>{e.preventDefault(),this.move(t.offsetLeft-t.clientWidth)}))}))}move(t){this.scrollbar.scrollTo({top:0,left:t,behavior:"smooth"})}constructor(){super(),this.scrollbar=this.querySelector(ni),this.arrowNext=this.querySelector(ai),this.arrowPrev=this.querySelector(ri),this.toggleNextArrow=this.toggleNextArrow.bind(this),this.addEventListener("theme:swatches:loaded",this.toggleNextArrow)}});const hi="[data-popout-list]",ui="[data-popout-toggle]",pi="[data-popout-toggle-text]",mi="[data-popout-input]",vi="[data-popout-option]",gi="[data-product-image]",bi="[data-grid-item]",wi="popout-list--visible",fi="is-visible",yi="is-active",Li="popout-list--top",Ei="aria-expanded",Si="aria-current",Ai="data-value",ki="data-popout-toggle-text",Ci="submit";customElements.get("popout-select")||customElements.define("popout-select",class extends HTMLElement{connectedCallback(){this.popoutList=this.querySelector(hi),this.popoutToggle=this.querySelector(ui),this.popoutToggleText=this.querySelector(pi),this.popoutInput=this.querySelector(mi)||this.parentNode.querySelector(mi),this.popoutOptions=this.querySelectorAll(vi),this.productGridItem=this.popoutList.closest(bi),this.fireSubmitEvent=this.hasAttribute(Ci),this.popupToggleFocusoutEvent=t=>this.onPopupToggleFocusout(t),this.popupListFocusoutEvent=t=>this.onPopupListFocusout(t),this.popupToggleClickEvent=t=>this.onPopupToggleClick(t),this.keyUpEvent=t=>this.onKeyUp(t),this.bodyClickEvent=t=>this.onBodyClick(t),this._connectOptions(),this._connectToggle(),this._onFocusOut(),this.popupListSetDimensions()}onPopupToggleClick(t){const e="true"===t.currentTarget.getAttribute(Ei);if(this.productGridItem){const t=this.productGridItem.querySelector(gi);t&&t.classList.toggle(fi,!e),this.popoutList.style.maxHeight=`${Math.abs(this.popoutToggle.getBoundingClientRect().bottom-this.productGridItem.getBoundingClientRect().bottom)}px`}t.currentTarget.setAttribute(Ei,!e),this.popoutList.classList.toggle(wi),this.popupListSetDimensions(),this.toggleListPosition(),document.body.addEventListener("click",this.bodyClickEvent)}onPopupToggleFocusout(t){this.contains(t.relatedTarget)||this._hideList()}onPopupListFocusout(t){const e=t.currentTarget.contains(t.relatedTarget);this.popoutList.classList.contains(wi)&&!e&&this._hideList()}toggleListPosition(){const t=this.querySelector(ui),e=this.getBoundingClientRect().top+this.clientHeight,s=()=>{"true"!==t.getAttribute(Ei)&&this.popoutList.classList.remove(Li),this.popoutList.removeEventListener("transitionend",s)};"true"===t.getAttribute(Ei)?window.innerHeight/2<e&&this.popoutList.classList.add(Li):this.popoutList.addEventListener("transitionend",s)}popupListSetDimensions(){this.popoutList.style.setProperty("--max-width","100vw"),this.popoutList.style.setProperty("--max-height","100vh"),requestAnimationFrame((()=>{this.popoutList.style.setProperty("--max-width",`${parseInt(document.body.clientWidth-this.popoutList.getBoundingClientRect().left)}px`),this.popoutList.style.setProperty("--max-height",`${parseInt(document.body.clientHeight-this.popoutList.getBoundingClientRect().top)}px`)}))}popupOptionsClick(t){if("#"===t.target.closest(vi).attributes.href.value){t.preventDefault();const e=t.currentTarget.hasAttribute(Ai)?t.currentTarget.getAttribute(Ai):"";if(this.popoutInput.value=e,this.popoutInput.disabled&&this.popoutInput.removeAttribute("disabled"),this.fireSubmitEvent)this._submitForm(e);else{const s=t.currentTarget.parentElement,i=this.popoutList.querySelector(`.${yi}`),o=this.popoutList.querySelector(`[${Si}]`);this.popoutInput.dispatchEvent(new Event("change")),i&&(i.classList.remove(yi),s.classList.add(yi)),"quantity"!=this.popoutInput.name||s.nextSibling||this.classList.add(yi),o&&o.hasAttribute(`${Si}`)&&(o.removeAttribute(`${Si}`),t.currentTarget.setAttribute(`${Si}`,"true")),""!==e&&(this.popoutToggleText.innerHTML=e,this.popoutToggleText.hasAttribute(ki)&&""!==this.popoutToggleText.getAttribute(ki)&&this.popoutToggleText.setAttribute(ki,e)),this.onPopupToggleFocusout(t),this.onPopupListFocusout(t)}}}onKeyUp(t){"Escape"===t.code&&(this._hideList(),this.popoutToggle.focus())}onBodyClick(t){const e=this.contains(t.target);this.popoutList.classList.contains(wi)&&!e&&this._hideList()}_connectToggle(){this.popoutToggle.addEventListener("click",this.popupToggleClickEvent)}_connectOptions(){this.popoutOptions.length&&this.popoutOptions.forEach((t=>{t.addEventListener("click",(t=>this.popupOptionsClick(t)))}))}_onFocusOut(){this.addEventListener("keyup",this.keyUpEvent),this.popoutToggle.addEventListener("focusout",this.popupToggleFocusoutEvent),this.popoutList.addEventListener("focusout",this.popupListFocusoutEvent)}_submitForm(){const t=this.closest("form");t&&t.submit()}_hideList(){this.popoutList.classList.remove(wi),this.popoutToggle.setAttribute(Ei,!1),this.toggleListPosition(),document.body.removeEventListener("click",this.bodyClickEvent)}constructor(){super()}});const qi={open:"[data-popup-open]",close:"[data-popup-close]",dialog:"dialog",focusable:'button, [href], select, textarea, [tabindex]:not([tabindex="-1"])',newsletterForm:"[data-newsletter-form]",newsletterHeading:"[data-newsletter-heading]",newsletterField:"[data-newsletter-field]"},Ti={closing:"closing",delay:"data-popup-delay",scrollLock:"data-scroll-lock-required",cookieName:"data-cookie-name",cookieValue:"data-cookie-value",preventTopLayer:"data-prevent-top-layer"},Mi="hidden",xi="has-value",Ii="cart-bar-visible",Oi="is-visible",Pi="has-success",Hi="mobile",Di="desktop",Fi="bottom";let _i=class extends HTMLElement{checkTargetReferrer(){this.popup.hasAttribute(Ti.referrer)&&(-1!==location.href.indexOf(this.popup.getAttribute(Ti.referrer))||window.Shopify.designMode||this.popup.parentNode.removeChild(this.popup))}checkCookie(){this.cookie&&!1!==this.cookie.read()||(this.showPopupEvents(),this.popup.addEventListener("theme:popup:onclose",(()=>this.cookie.write())))}bindListeners(){var t,e;null===(t=this.buttonPopupOpen)||void 0===t||t.addEventListener("click",(t=>{t.preventDefault(),this.popupOpen(),window.theme.a11y.lastElement=this.buttonPopupOpen})),null===(e=this.popup.querySelectorAll(qi.close))||void 0===e||e.forEach((t=>{t.addEventListener("click",(t=>{t.preventDefault(),this.popupClose()}))})),this.popup.addEventListener("click",(t=>{"DIALOG"===t.target.nodeName&&"click"===t.type&&this.popupClose()})),this.popup.addEventListener("keydown",(t=>{"Escape"===t.code&&(t.preventDefault(),this.popupClose())})),this.popup.addEventListener("close",(()=>this.popupCloseActions()))}popupOpen(){this.isAnimating=!0,"function"!=typeof this.popup.showModal||this.preventTopLayer?"function"==typeof this.popup.show?this.popup.show():this.popup.setAttribute("open",""):this.popup.showModal(),this.popup.removeAttribute("inert"),this.popup.setAttribute("aria-hidden",!1),this.popup.focus(),this.enableScrollLock&&document.dispatchEvent(new CustomEvent("theme:scroll:lock",{bubbles:!0})),window.theme.waitForAnimationEnd(this.popup).then((()=>{this.isAnimating=!1,this.enableScrollLock&&this.a11y.trapFocus(this.popup);const t=this.popup.querySelector("[autofocus]")||this.popup.querySelector(qi.focusable);null==t||t.focus()}))}popupClose(){if(!this.isAnimating&&!this.popup.hasAttribute("inert")){if(!this.popup.hasAttribute(Ti.closing))return this.popup.setAttribute(Ti.closing,""),this.isAnimating=!0,void window.theme.waitForAnimationEnd(this.popup).then((()=>{this.isAnimating=!1,this.popupClose()}));"function"==typeof this.popup.close?this.popup.close():(this.popup.removeAttribute("open"),this.popup.setAttribute("aria-hidden",!0)),this.popupCloseActions()}}popupCloseActions(){this.popup.hasAttribute("inert")||(this.popup.setAttribute("inert",""),this.popup.setAttribute("aria-hidden",!0),this.popup.removeAttribute(Ti.closing),!window.theme.hasOpenModals()&&this.enableScrollLock&&document.dispatchEvent(new CustomEvent("theme:scroll:unlock",{bubbles:!0})),this.popup.dispatchEvent(new CustomEvent("theme:popup:onclose",{bubbles:!1})),this.enableScrollLock&&(this.a11y.removeTrapFocus(),this.a11y.autoFocusLastElement()))}showPopupEvents(){this.popup.hasAttribute("open")&&1==this.popup.getAttribute("open")&&this.popupOpen(),this.delay=this.popup.hasAttribute(Ti.delay)?this.popup.getAttribute(Ti.delay):null,this.isSubmitted=-1!==window.location.href.indexOf("accepts_marketing")||-1!==window.location.href.indexOf("customer_posted=true"),this.showOnScrollEvent=window.theme.throttle(this.showOnScroll.bind(this),200),("always"===this.delay||this.isSubmitted)&&this.popupOpen(),this.delay&&this.delay.includes("delayed")&&!this.isSubmitted&&this.showDelayed(),"bottom"!==this.delay||this.isSubmitted||this.showOnBottomReached(),"idle"!==this.delay||this.isSubmitted||this.showOnIdle()}showDelayed(){const t=this.delay.includes("_")?parseInt(this.delay.split("_")[1]):10;setTimeout((()=>{this.popupOpen()}),1e3*t)}showOnIdle(){let t=0;const e=["mousemove","mousedown","click","touchmove","touchstart","touchend","keydown","keypress"],s=["load","resize","scroll"],i=()=>{t=setTimeout((()=>{t=0,this.popupOpen()}),6e4),e.forEach((t=>{document.addEventListener(t,o)})),s.forEach((t=>{window.addEventListener(t,o)}))},o=()=>{t&&clearTimeout(t),e.forEach((t=>{document.removeEventListener(t,o)})),s.forEach((t=>{window.removeEventListener(t,o)})),i()};i()}showOnBottomReached(){document.addEventListener("theme:scroll",this.showOnScrollEvent)}showOnScroll(){window.scrollY+window.innerHeight>=document.body.clientHeight&&(this.popupOpen(),document.removeEventListener("theme:scroll",this.showOnScrollEvent))}disconnectedCallback(){document.removeEventListener("theme:scroll",this.showOnScrollEvent)}constructor(){super(),this.popup=this.querySelector(qi.dialog),this.preventTopLayer=this.popup.hasAttribute(Ti.preventTopLayer),this.enableScrollLock=this.popup.hasAttribute(Ti.scrollLock),this.buttonPopupOpen=this.querySelector(qi.open),this.a11y=window.theme.a11y,this.isAnimating=!1,this.cookie=new class{write(){(-1!==document.cookie.indexOf("; ")&&!document.cookie.split("; ").find((t=>t.startsWith(this.name)))||-1===document.cookie.indexOf("; "))&&(document.cookie=`${this.name}=${this.value}; expires=${this.config.expires}; path=${this.config.path}; domain=${this.config.domain}; sameSite=${this.config.sameSite}; secure=${this.config.secure}`)}read(){return!(-1===document.cookie.indexOf("; ")||!document.cookie.split("; ").find((t=>t.startsWith(this.name))))&&document.cookie.split("; ").find((t=>t.startsWith(this.name))).split("=")[1]}destroy(){document.cookie.split("; ").find((t=>t.startsWith(this.name)))&&(document.cookie=`${this.name}=null; expires=${this.config.expires}; path=${this.config.path}; domain=${this.config.domain}`)}constructor(t,e,s=7){const i=new Date,o=new Date;o.setTime(i.getTime()+864e5*s),this.config={expires:o.toGMTString(),path:"/",domain:window.location.hostname,sameSite:"none",secure:!0},this.name=t,this.value=e}}(this.popup.getAttribute(Ti.cookieName),this.popup.getAttribute(Ti.cookieValue)),this.checkTargetReferrer(),this.checkCookie(),this.bindListeners()}},$i=class extends _i{connectedCallback(){var t;const e=!1!==(null===(t=this.cookie)||void 0===t?void 0:t.read()),s=-1!==window.location.search.indexOf("?customer_posted=true"),i=[...this.classList].toString().includes(Fi),o=this.popup.classList.contains(Hi),n=this.popup.classList.contains(Di),r=window.theme.isMobile();let a=!0;if((o&&!r||n&&r)&&(a=!1),!a)return super.a11y.removeTrapFocus(),void document.dispatchEvent(new CustomEvent("theme:scroll:unlock",{bubbles:!0}));e&&!window.Shopify.designMode||(window.Shopify.designMode||window.location.pathname.endsWith("/challenge")||super.showPopupEvents(),this.form&&this.form.classList.contains(Pi)&&(super.popupOpen(),this.cookie.write()),this.popup.addEventListener("theme:popup:onclose",(()=>this.cookie.write()))),s&&(this.delay=0),e&&!window.Shopify.designMode||(this.show(),this.form.classList.contains(Pi)&&(this.popupOpen(),this.cookie.write())),i&&this.observeCartBar()}show(){window.location.pathname.endsWith("/challenge")||(window.Shopify.designMode?super.popupOpen():super.showPopupEvents()),this.showForm(),this.inputField(),this.popup.addEventListener("theme:popup:onclose",(()=>this.cookie.write()))}observeCartBar(){if(this.cartBar=document.getElementById(qi.cartBar),!this.cartBar)return;let t=this.cartBar.classList.contains(Oi);document.body.classList.toggle(Ii,t);this.observer=new MutationObserver((e=>{for(const s of e)"attributes"===s.type&&(t=s.target.classList.contains(Oi),document.body.classList.toggle(Ii,t))})),this.observer.observe(this.cartBar,{attributes:!0,childList:!1,subtree:!1})}showForm(){var t,e;null===(t=this.heading)||void 0===t||t.addEventListener("click",(t=>{t.preventDefault(),this.heading.classList.add(Mi),this.form.classList.remove(Mi),this.newsletterField.focus()})),null===(e=this.heading)||void 0===e||e.addEventListener("keyup",(t=>{"Enter"===t.code&&this.heading.dispatchEvent(new Event("click"))}))}inputField(){const t=()=>{this.resetClassTimer&&clearTimeout(this.resetClassTimer),""!==this.newsletterField.value&&this.popup.classList.add(xi)};this.newsletterField.addEventListener("input",t),this.newsletterField.addEventListener("focus",t),this.newsletterField.addEventListener("focusout",(()=>{this.resetClassTimer&&clearTimeout(this.resetClassTimer),this.resetClassTimer=setTimeout((()=>{this.popup.classList.remove(xi)}),2e3)}))}disconnectedCallback(){this.observer&&this.observer.disconnect()}constructor(){super(),this.form=this.popup.querySelector(qi.newsletterForm),this.heading=this.popup.querySelector(qi.newsletterHeading),this.newsletterField=this.popup.querySelector(qi.newsletterField)}};customElements.get("popup-component")||customElements.define("popup-component",_i),customElements.get("popup-newsletter")||customElements.define("popup-newsletter",$i);const Bi='[role="option"]',Wi='[aria-selected="true"]',Ni="[data-popular-searches]",Ri="predictive-search",Ui="[data-predictive-search-results]",Vi="[data-predictive-search-status]",ji='input[type="search"]',zi="[data-popdown]",Ji="[data-predictive-search-live-region-count-value]",Xi="[data-search-results-groups-wrapper]",Qi="[data-predictive-search-search-for-text]",Yi="#shopify-section-predictive-search",Ki='[aria-selected="true"] a',Gi='[aria-selected="true"] a, button[aria-selected="true"]';function Zi(){this.entries=[]}function to(t,e){eo(t);var s=function(t,e){eo(t),function(t){if(!Array.isArray(t))throw new TypeError(t+" is not an array.");if(0===t.length)throw new Error(t+" is empty.");if(!t[0].hasOwnProperty("name"))throw new Error(t[0]+"does not contain name key.");if("string"!=typeof t[0].name)throw new TypeError("Invalid value type passed for name of option "+t[0].name+". Value should be string.")}(e);var s=[];return e.forEach((function(e){for(var i=0;i<t.options.length;i++){if((t.options[i].name||t.options[i]).toLowerCase()===e.name.toLowerCase()){s[i]=e.value;break}}})),s}(t,e);return function(t,e){return eo(t),function(t){if(Array.isArray(t)&&"object"==typeof t[0])throw new Error(t+"is not a valid array of options.")}(e),t.variants.filter((function(t){return e.every((function(e,s){return t.options[s]===e}))}))[0]||null}(t,s)}function eo(t){if("object"!=typeof t)throw new TypeError(t+" is not an object.");if(0===Object.keys(t).length&&t.constructor===Object)throw new Error(t+" is empty.")}customElements.get("predictive-search")||customElements.define("predictive-search",class extends si{connectedCallback(){this.input.addEventListener("focus",this.onFocus.bind(this)),this.input.form.addEventListener("submit",this.onFormSubmit.bind(this)),this.addEventListener("focusout",this.onFocusOut.bind(this)),this.addEventListener("keyup",this.onKeyup.bind(this)),this.addEventListener("keydown",this.onKeydown.bind(this))}getQuery(){return this.input.value.trim()}onChange(){super.onChange();const t=this.getQuery();var e;this.searchTerm&&t.startsWith(this.searchTerm)||(null===(e=this.querySelector(Xi))||void 0===e||e.remove());this.updateSearchForTerm(this.searchTerm,t),this.searchTerm=t,this.searchTerm.length?this.getSearchResults(this.searchTerm):this.reset()}onFormSubmit(t){this.getQuery().length&&!this.querySelector(Ki)||t.preventDefault()}onFormReset(t){super.onFormReset(t),super.shouldResetForm()&&(this.searchTerm="",this.abortController.abort(),this.abortController=new AbortController,this.closeResults(!0))}shouldResetForm(){return!document.querySelector(Ki)}onFocus(){const t=this.getQuery();t.length&&(this.searchTerm!==t?this.onChange():"true"===this.getAttribute("results")?this.open():this.getSearchResults(this.searchTerm))}onFocusOut(){setTimeout((()=>{this.contains(document.activeElement)||this.close()}))}onKeyup(t){switch(this.getQuery().length||this.close(!0),t.preventDefault(),t.code){case"ArrowUp":this.switchOption("up");break;case"ArrowDown":this.switchOption("down");break;case"Enter":this.selectOption()}}onKeydown(t){"ArrowUp"!==t.code&&"ArrowDown"!==t.code||t.preventDefault()}updateSearchForTerm(t,e){const s=this.querySelector(Qi),i=null==s?void 0:s.innerText;if(i){var o;if((null===(o=i.match(new RegExp(t,"g")))||void 0===o?void 0:o.length)>1)return;const n=i.replace(t,e);s.innerText=n}}switchOption(t){if(!this.getAttribute("open"))return;const e="up"===t,s=this.querySelector(Wi),i=Array.from(this.querySelectorAll(Bi)).filter((t=>null!==t.offsetParent));let o=0;if(e&&!s)return;let n=-1,r=0;for(;-1===n&&r<=i.length;)i[r]===s&&(n=r),r++;if(this.statusElement.textContent="",!e&&s?o=n===i.length-1?0:n+1:e&&(o=0===n?i.length-1:n-1),o===n)return;const a=i[o];a.setAttribute("aria-selected",!0),s&&s.setAttribute("aria-selected",!1),this.input.setAttribute("aria-activedescendant",a.id)}selectOption(){const t=this.querySelector(Gi);t&&t.click()}getSearchResults(t){const e=t.replace(" ","-").toLowerCase();this.setLiveRegionLoadingState(),this.cachedResults[e]?this.renderSearchResults(this.cachedResults[e]):fetch(`${theme.routes.predictive_search_url}?q=${encodeURIComponent(t)}&section_id=predictive-search`,{signal:this.abortController.signal}).then((t=>{if(!t.ok){var e=new Error(t.status);throw this.close(),e}return t.text()})).then((t=>{const s=(new DOMParser).parseFromString(t,"text/html").querySelector(Yi).innerHTML;this.allPredictiveSearchInstances.forEach((t=>{t.cachedResults[e]=s})),this.renderSearchResults(s)})).catch((t=>{if(20!==(null==t?void 0:t.code))throw this.close(),t}))}setLiveRegionLoadingState(){this.statusElement=this.statusElement||this.querySelector(Vi),this.loadingText=this.loadingText||this.getAttribute("data-loading-text"),this.setLiveRegionText(this.loadingText),this.setAttribute("loading",!0)}setLiveRegionText(t){this.statusElement.setAttribute("aria-hidden","false"),this.statusElement.textContent=t,setTimeout((()=>{this.statusElement.setAttribute("aria-hidden","true")}),1e3)}renderSearchResults(t){this.predictiveSearchResults.innerHTML=t,this.setAttribute("results",!0),this.setLiveRegionResults(),this.open()}setLiveRegionResults(){this.removeAttribute("loading"),this.setLiveRegionText(this.querySelector(Ji).textContent)}open(){this.setAttribute("open",!0),this.input.setAttribute("aria-expanded",!0),this.isOpen=!0,this.predictiveSearchResults.style.setProperty("--full-screen",`${window.visualViewport.height}px`)}close(t=!1){this.closeResults(t),this.isOpen=!1,this.predictiveSearchResults.style.removeProperty("--full-screen")}closeResults(t=!1){var e;t&&(this.input.value="",this.removeAttribute("results"));const s=this.querySelector(Wi);s&&s.setAttribute("aria-selected",!1),this.input.setAttribute("aria-activedescendant",""),this.removeAttribute("loading"),this.removeAttribute("open"),this.input.setAttribute("aria-expanded",!1),null===(e=this.predictiveSearchResults)||void 0===e||e.removeAttribute("style")}reset(){this.predictiveSearchResults.innerHTML="",this.input.val="",this.a11y.removeTrapFocus(),this.popularSearches&&(this.input.dispatchEvent(new Event("blur",{bubbles:!1})),this.a11y.trapFocus(this.searchPopdown,{elementToFocus:this.input}))}constructor(){var t;super(),this.a11y=window.theme.a11y,this.abortController=new AbortController,this.allPredictiveSearchInstances=document.querySelectorAll(Ri),this.cachedResults={},this.input=this.querySelector(ji),this.isOpen=!1,this.predictiveSearchResults=this.querySelector(Ui),this.searchPopdown=this.closest(zi),this.popularSearches=null===(t=this.searchPopdown)||void 0===t?void 0:t.querySelector(Ni),this.searchTerm=""}}),Zi.prototype.add=function(t,e,s){this.entries.push({element:t,event:e,fn:s}),t.addEventListener(e,s)},Zi.prototype.removeAll=function(){this.entries=this.entries.filter((function(t){return t.element.removeEventListener(t.event,t.fn),!1}))};var so='[name="id"]',io='[name="selling_plan"]',oo='[name^="options"]',no='[name="quantity"]',ro='[name^="properties"]';const ao="data-option-position",lo='[name^="options"], [data-popout-option]',co='[name^="options"]:checked, [name^="options"][type="hidden"]',ho="data-value",uo="[data-popout]",po="sold-out",mo="unavailable",vo="sale";const go="[data-product]",bo="[data-product-form]",wo="product-notification",fo="[data-variant-title]",yo="[data-notification-product]",Lo="[data-add-to-cart]",Eo="[data-add-to-cart-text]",So="[data-compare-price]",Ao="[data-compare-text]",ko="[data-final-sale-badge]",Co="[data-form-wrapper]",qo="[data-product-select]",To="[data-price-wrapper]",Mo="product-images",xo="[data-product-media-list]",Io="[data-product-json]",Oo="[data-product-price]",Po="[data-product-unit-price]",Ho="[data-product-base]",Do="[data-product-unit]",Fo="[data-product-preorder]",_o="[data-subscription-watch-price]",$o="[data-subscription-selectors]",Bo="[data-toggles-group]",Wo="data-group-toggle",No="[data-plan-description]",Ro="[data-section-type]",Uo="[data-variant-sku]",Vo="[data-variant-final-sale-metafield]",jo="[data-variant-buttons]",zo="[data-variant-option-image]",Jo="[data-quick-add-modal]",Xo="[data-price-off-amount]",Qo="[data-price-off-badge]",Yo="[data-price-off-type]",Ko="[data-price-off]",Go="[data-remaining-count]",Zo="[data-remaining-max]",tn="[data-remaining-wrapper]",en="[data-product-remaining-json]",sn="[data-option-value]",on="[data-option-position]",nn="[data-product-form-installment]",rn='input[name="id"]',an="hidden",ln="variant--soldout",dn="variant--unavailable",cn="product__price--sale",hn="count-is-low",un="count-is-in",pn="count-is-out",mn="count-is-unavailable",vn="data-remaining-max",gn="data-enable-history-state",bn="data-fader-desktop",wn="data-fader-mobile",fn="data-option-position",yn="data-image-id",Ln="data-media-id",En="data-quick-add-btn",Sn="data-final-sale",An="data-variant-image-scroll";customElements.get("product-form")||customElements.define("product-form",class extends HTMLElement{connectedCallback(){if(this.cartAddEvents(),this.container=this.closest(Ro)||this.closest(Jo),!this.container)return;if(this.sectionId=this.container.dataset.sectionId,this.product=this.container.querySelector(go),this.productForm=this.container.querySelector(bo),this.productNotification=this.container.querySelector(wo),this.productImages=this.container.querySelector(Mo),this.productMediaList=this.container.querySelector(xo),this.installmentForm=this.container.querySelector(nn),this.skuWrapper=this.container.querySelector(Uo),this.sellout=null,this.variantImageScroll="true"===this.container.getAttribute(An),this.priceOffWrap=this.container.querySelector(Ko),this.priceOffAmount=this.container.querySelector(Xo),this.priceOffType=this.container.querySelector(Yo),this.planDescription=this.container.querySelector(No),this.remainingWrapper=this.container.querySelector(tn),this.remainingWrapper){const t=this.container.querySelector(Zo);t&&(this.remainingMaxInt=parseInt(t.getAttribute(vn),10),this.remainingCount=this.container.querySelector(Go),this.remainingJSONWrapper=this.container.querySelector(en),this.remainingJSON=null,this.remainingJSONWrapper&&""!==this.remainingJSONWrapper.innerHTML?this.remainingJSON=JSON.parse(this.remainingJSONWrapper.innerHTML):console.warn("Missing product quantity JSON"))}this.enableHistoryState="true"===this.container.getAttribute(gn),this.hasUnitPricing=this.container.querySelector(Do),this.subSelectors=this.container.querySelector($o),this.subPrices=this.container.querySelector(_o),this.isPreOrder=this.container.querySelector(Fo);let t=null;const e=this.container.querySelector(Io);e&&(t=e.innerHTML),t?(this.productJSON=JSON.parse(t),this.linkForm(),this.sellout=new class{init(){this.update()}update(){this.getCurrentState(),this.optionElements.forEach((t=>{const e=t.closest(`[${ao}]`);if(!e)return;const s=t.value||t.getAttribute(ho),i=e.getAttribute(ao),o=parseInt(i,10)-1,n=t.closest(uo);let r=[...this.selections];r[o]=s;const a=this.productJSON.variants.find((t=>{let e=!0;for(let s=0;s<r.length;s++)t.options[s]!==r[s]&&(e=!1);return e}));t.classList.remove(po,mo),t.parentNode.classList.remove(vo),n&&n.classList.remove(po,mo,vo),void 0===a?(t.classList.add(mo),n&&n.classList.add(mo)):a&&!1===a.available&&(t.classList.add(po),n&&n.classList.add(po)),a&&a.compare_at_price>a.price&&theme.settings.variantOnSale&&t.parentNode.classList.add(vo)}))}getCurrentState(){this.selections=[];const t=this.container.querySelectorAll(co);t.length&&t.forEach((t=>{const e=t.value;e&&""!==e&&this.selections.push(e)}))}constructor(t,e){this.container=t,this.productJSON=e,this.optionElements=this.container.querySelectorAll(lo),this.productJSON&&this.optionElements.length&&this.init()}}(this.container,this.productJSON)):console.error("Missing product JSON"),this.variantOptionImages=this.container.querySelectorAll(zo),this.variantButtons=this.container.querySelectorAll(jo),this.variantOptionImages.length>1&&this.optionImagesWidth()}cartAddEvents(){this.buttonATC=this.querySelector(Lo),this.buttonATC.addEventListener("click",(t=>{t.preventDefault(),document.dispatchEvent(new CustomEvent("theme:cart:add",{detail:{button:this.buttonATC},bubbles:!1})),this.closest(Jo)||(window.a11y.lastElement=this.buttonATC)}))}destroy(){this.productForm.destroy()}linkForm(){this.productForm=new class{destroy(){this._listeners.removeAll()}options(){return this._serializeInputValues(this.optionInputs,(function(t){return t.name=/(?:^(options\[))(.*?)(?:\])/.exec(t.name)[2],t}))}variant(){const t=this.options();return t.length?to(this.product,t):this.product.variants[0]}plan(t){let e={allocation:null,group:null,detail:null};const s=this.element.querySelector(`${io}:checked`);if(!s)return null;const i=s.value,o=i&&""!==i?i:null;return o&&t&&(e.allocation=t.selling_plan_allocations.find((function(t){return t.selling_plan_id.toString()===o.toString()}))),e.allocation&&(e.group=this.product.selling_plan_groups.find((function(t){return t.id.toString()===e.allocation.selling_plan_group_id.toString()}))),e.group&&(e.detail=e.group.selling_plans.find((function(t){return t.id.toString()===o.toString()}))),e&&e.allocation&&e.detail&&e.allocation?e:null}properties(){return this._serializeInputValues(this.propertyInputs,(function(t){return t.name=/(?:^(properties\[))(.*?)(?:\])/.exec(t.name)[2],t}))}quantity(){return this.quantityInputs[0]?Number.parseInt(this.quantityInputs[0].value,10):1}getFormState(){const t=this.variant();return{options:this.options(),variant:t,properties:this.properties(),quantity:this.quantity(),plan:this.plan(t)}}_setIdInputValue(t){t&&t.id?this.variantElement.value=t.id.toString():this.variantElement.value="",this.variantElement.dispatchEvent(new Event("change"))}_onSubmit(t,e){e.dataset=this.getFormState(),t.onFormSubmit&&t.onFormSubmit(e)}_onOptionChange(t){this._setIdInputValue(t.dataset.variant)}_onFormEvent(t){return void 0===t?Function.prototype.bind():function(e){e.dataset=this.getFormState(),this._setIdInputValue(e.dataset.variant),t(e)}.bind(this)}_initInputs(t,e){return Array.prototype.slice.call(this.element.querySelectorAll(t)).map(function(t){return this._listeners.add(t,"change",this._onFormEvent(e)),t}.bind(this))}_serializeInputValues(t,e){return t.reduce((function(t,s){return(s.checked||"radio"!==s.type&&"checkbox"!==s.type)&&t.push(e({name:s.name,value:s.value})),t}),[])}_validateProductObject(t){if("object"!=typeof t)throw new TypeError(t+" is not an object.");if(void 0===t.variants[0].options)throw new TypeError("Product object is invalid. Make sure you use the product object that is output from {{ product | json }} or from the http://[your-product-url].js route");return t}constructor(t,e,s){this.element=t,this.product=this._validateProductObject(e),this.variantElement=this.element.querySelector(so),s=s||{},this._listeners=new Zi,this._listeners.add(this.element,"submit",this._onSubmit.bind(this,s)),this.optionInputs=this._initInputs(oo,s.onOptionChange),this.planInputs=this._initInputs(io,s.onPlanChange),this.quantityInputs=this._initInputs(no,s.onQuantityChange),this.propertyInputs=this._initInputs(ro,s.onPropertyChange)}}(this.container,this.productJSON,{onOptionChange:this.onOptionChange.bind(this),onPlanChange:this.onPlanChange.bind(this)}),this.pushState(this.productForm.getFormState(),!0),this.subsToggleListeners()}onOptionChange(t){this.pushState(t.dataset)}onPlanChange(t){this.subPrices&&this.pushState(t.dataset)}pushState(t,e=!1){var s;this.productState=this.setProductState(t),this.updateAddToCartState(t),this.updateNotificationForm(t),this.updateProductPrices(t),this.updateProductImage(t),this.updateSaleText(t),this.updateSku(t),this.updateSubscriptionText(t),this.updateRemaining(t),this.updateLegend(t),this.fireHookEvent(t),null===(s=this.sellout)||void 0===s||s.update(t),this.enableHistoryState&&!e&&this.updateHistoryState(t)}updateAddToCartState(t){const e=t.variant;let s=theme.strings.addToCart;const i=this.container.querySelectorAll(To),o=this.container.querySelectorAll(Lo),n=this.container.querySelectorAll(Eo),r=this.container.querySelectorAll(Co);if(this.installmentForm&&e){const t=this.installmentForm.querySelector(rn);t.value=e.id,t.dispatchEvent(new Event("change",{bubbles:!0}))}this.isPreOrder&&(s=theme.strings.preOrder),i.length&&e&&i.forEach((t=>{t.classList.remove(an)})),null==o||o.forEach((t=>{t.hasAttribute(En)||(e&&e.available?t.disabled=!1:t.disabled=!0)})),null==n||n.forEach((t=>{let i=s;e?e.available||(i=theme.strings.soldOut):i=theme.strings.unavailable,t.textContent=i})),r.length&&r.forEach((t=>{if(e){e.available?t.classList.remove(ln,dn):(t.classList.add(ln),t.classList.remove(dn));const s=t.querySelector(qo);s&&(s.value=e.id);const i=t.querySelector(`${rn}[form]`);i&&(i.value=e.id,i.dispatchEvent(new Event("change")))}else t.classList.add(dn),t.classList.remove(ln)}))}updateNotificationForm(t){if(!this.productNotification)return;const e=this.productNotification.querySelector(fo),s=this.productNotification.querySelector(yo);null!=e&&(e.textContent=t.variant.title,s.value=t.variant.name)}updateHistoryState(t){const e=t.variant,s=t.plan,i=window.location.href;if(e&&i.includes("/product")){const t=new window.URL(i),o=t.searchParams;o.set("variant",e.id),s&&s.detail&&s.detail.id&&this.productState.hasPlan?o.set("selling_plan",s.detail.id):o.delete("selling_plan"),t.search=o.toString();const n=t.toString();window.history.replaceState({path:n},"",n)}}updateRemaining(t){var e;const s=t.variant;if(null===(e=this.remainingWrapper)||void 0===e||e.classList.remove(un,pn,mn,hn),s&&this.remainingWrapper&&this.remainingJSON){const t=this.remainingJSON[s.id];("out"===t||t<1)&&this.remainingWrapper.classList.add(pn),("in"===t||t>=this.remainingMaxInt)&&this.remainingWrapper.classList.add(un),("low"===t||t>0&&t<this.remainingMaxInt)&&(this.remainingWrapper.classList.add(hn),this.remainingCount&&(this.remainingCount.innerHTML=t))}else!s&&this.remainingWrapper&&this.remainingWrapper.classList.add(mn)}optionImagesWidth(){if(!this.variantButtons)return;let t=0;requestAnimationFrame((()=>{this.variantOptionImages.forEach((e=>{const s=e.clientWidth;s>t&&(t=s)})),this.variantButtons.forEach((e=>{var s;null===(s=e.style)||void 0===s||s.setProperty("--option-image-width",t+"px")}))}))}getBaseUnit(t){return 1===t.unit_price_measurement.reference_value?t.unit_price_measurement.reference_unit:t.unit_price_measurement.reference_value+t.unit_price_measurement.reference_unit}subsToggleListeners(){this.container.querySelectorAll(Bo).forEach((t=>{t.addEventListener("change",function(t){const e=t.target.value.toString(),s=this.container.querySelector(`[${Wo}="${e}"]`),i=this.container.querySelectorAll(`[${Wo}]`);if(s){s.classList.remove(an);const t=s.querySelector('[name="selling_plan"]');t.checked=!0,t.dispatchEvent(new Event("change"))}i.forEach((t=>{if(t!==s){t.classList.add(an);t.querySelectorAll('[name="selling_plan"]').forEach((t=>{t.checked=!1,t.dispatchEvent(new Event("change"))}))}}))}.bind(this))}))}updateSaleText(t){this.priceOffWrap&&(this.productState.planSale?this.updateSaleTextSubscription(t):this.productState.onSale?this.updateSaleTextStandard(t):this.priceOffWrap.classList.add(an))}isVariantFinalSale(t){var e;const s=null===(e=document.querySelector(Vo))||void 0===e?void 0:e.textContent;if(!s)return;const i=JSON.parse(s);let o=!1;return i.forEach((e=>{Number(e.variant_id)===t.id&&(o="true"===e.metafield_value)})),o}updateSaleTextStandard(t){var e,s,i;const o=t.variant,n=null===(e=this.priceOffWrap)||void 0===e?void 0:e.querySelector(ko),r=null===(s=this.priceOffWrap)||void 0===s?void 0:s.querySelector(Qo),a=null==o?void 0:o.compare_at_price,l=null==o?void 0:o.price;if(this.priceOffType&&(this.priceOffType.innerHTML=window.theme.strings.sale||"sale"),!r||!this.priceOffAmount||!a||a<=l)null==r||r.classList.add(an);else{const t=Math.round((a-l)/a*100);this.priceOffAmount.innerHTML=`${t}%`,r.classList.remove(an)}const d=(null===(i=this.priceOffWrap)||void 0===i?void 0:i.hasAttribute(Sn))||this.isVariantFinalSale(o);n&&n.classList.toggle(an,!d),this.priceOffWrap.classList.remove(an)}updateSubscriptionText(t){t.plan&&this.planDescription?(this.planDescription.innerHTML=t.plan.detail.description,this.planDescription.classList.remove(an)):this.planDescription&&this.planDescription.classList.add(an)}updateSaleTextSubscription(t){if(this.priceOffType&&(this.priceOffType.innerHTML=window.theme.strings.subscription||"subscripton"),this.priceOffAmount&&this.priceOffWrap){const e=t.plan.detail.price_adjustments[0],s=e.value;e&&"percentage"===e.value_type?this.priceOffAmount.innerHTML=`${s}%`:this.priceOffAmount.innerHTML=window.theme.formatMoney(s,theme.moneyFormat),this.priceOffWrap.classList.remove(an)}}updateProductPrices(t){const e=t.variant,s=t.plan;this.container.querySelectorAll(To).forEach((t=>{const i=t.querySelector(So),o=t.querySelector(Oo),n=t.querySelector(Ao);let r="",a="";this.productState.available&&(r=e.compare_at_price,a=e.price),this.productState.hasPlan&&(a=s.allocation.price),this.productState.planSale&&(r=s.allocation.compare_at_price,a=s.allocation.price),i&&(this.productState.onSale||this.productState.planSale?(i.classList.remove(an),n.classList.remove(an),o.classList.add(cn)):(i.classList.add(an),n.classList.add(an),o.classList.remove(cn)),i.innerHTML=window.theme.formatMoney(r,theme.moneyFormat)),o.innerHTML=0===a?window.theme.strings.free:window.theme.formatMoney(a,theme.moneyFormat)})),this.hasUnitPricing&&this.updateProductUnits(t)}updateProductUnits(t){const e=t.variant,s=t.plan;let i=null;if(e&&e.unit_price&&(i=e.unit_price),s&&s.allocation&&s.allocation.unit_price&&(i=s.allocation.unit_price),i){const t=this.getBaseUnit(e),s=window.theme.formatMoney(i,theme.moneyFormat);this.container.querySelector(Po).innerHTML=s,this.container.querySelector(Ho).innerHTML=t,this.container.querySelector(Do).classList.remove(an)}else this.container.querySelector(Do).classList.add(an)}updateSku(t){this.skuWrapper&&(this.skuWrapper.innerHTML=`${theme.strings.sku}: ${t.variant.sku}`)}fireHookEvent(t){const e=t.variant;this.container.dispatchEvent(new CustomEvent("theme:variant:change",{detail:{variant:e},bubbles:!0}))}setProductState(t){const e=t.variant,s=t.plan,i={available:!0,soldOut:!1,onSale:!1,showUnitPrice:!1,requiresPlan:!1,hasPlan:!1,planPerDelivery:!1,planSale:!1};return!e||e.requires_selling_plan&&!s?i.available=!1:(e.available||(i.soldOut=!0),e.compare_at_price>e.price&&(i.onSale=!0),e.unit_price&&(i.showUnitPrice=!0),this.product&&this.product.requires_selling_plan&&(i.requiresPlan=!0),s&&this.subPrices&&(i.hasPlan=!0,s.allocation.per_delivery_price!==s.allocation.price&&(i.planPerDelivery=!0),e.price>s.allocation.price&&(i.planSale=!0))),i}updateProductImage(t){var e;const s=(null===(e=t.dataset)||void 0===e?void 0:e.variant)||t.variant;if(s&&s.featured_media){const t=this.container.querySelector(`[${yn}="${s.featured_media.id}"]`);if(t){const e=t.getAttribute(Ln),s=!window.theme.isMobile();if(t.dispatchEvent(new CustomEvent("theme:media:select",{bubbles:!0,detail:{id:e}})),s&&!this.productImages.hasAttribute(bn)&&this.variantImageScroll){const e=t.getBoundingClientRect().top;document.dispatchEvent(new CustomEvent("theme:tooltip:close",{bubbles:!1,detail:{hideTransition:!1}})),window.theme.scrollTo(e)}s||this.productImages.hasAttribute(wn)||this.productMediaList.scrollTo({left:t.offsetLeft})}}}updateLegend(t){const e=t.variant;if(e){const t=this.container.querySelectorAll(sn);t.length&&t.forEach((t=>{const s=t.closest(on);if(s){const i=s.getAttribute(fn),o=parseInt(i,10)-1,n=e.options[o];t.innerHTML=n}}))}}constructor(){super()}});const kn="[data-grid-swatch-fieldset]",Cn="[data-grid-item]",qn="[data-product-information]",Tn="[data-product-image]",Mn="[data-swatch-button]",xn="[data-swatch-link]",In="[data-swatch-text]",On="[data-swatch-template]",Pn="native-scrollbar",Hn="is-visible",Dn="no-events",Fn="swatch",_n="data-swatch-handle",$n="data-swatch-label",Bn="data-swatch-count",Wn="data-swatch-variant-name",Nn="data-variant-title",Rn="data-swatch-values";let Un=class extends HTMLElement{connectedCallback(){this.handle=this.getAttribute(_n),this.nativeScrollbar=this.closest(Pn),this.productItem=this.closest(Cn),this.productInfo=this.closest(qn),this.productImage=this.productItem.querySelector(Tn),this.template=document.querySelector(On).innerHTML,this.swatchesJSON=this.getSwatchesJSON(),this.swatchesStyle=theme.settings.collectionSwatchStyle;const t=this.getAttribute($n).trim().toLowerCase();(function(t){const e=`${window.theme.routes.root}products/${t}.js`;return window.fetch(e).then((t=>t.json())).catch((t=>{console.error(t)}))})(this.handle).then((e=>{this.product=e,this.colorOption=e.options.find((function(e){return e.name.toLowerCase()===t||null})),this.colorOption&&this.init()}))}init(){if(this.innerHTML="",this.count=0,this.limitedCount=0,this.swatches=this.colorOption.values,this.swatchesCount=0,this.swatches.forEach((t=>{let e=null,s=!1,i="";for(const s of this.product.variants){const o=s.options.includes(t);if(!e&&o&&(e=s),o&&s.featured_media){i=s.featured_media.preview_image.src,e=s;break}}for(const e of this.product.variants){if(e.options.includes(t)&&e.available){s=!0;break}}if(e){const r=document.createElement("div");r.innerHTML=this.template;const a=r.querySelector(Mn),l=r.querySelector(xn),d=r.querySelector(In),c=this.swatchesJSON[t],h="native"==theme.settings.swatchesType?c:`var(--${c})`,u=e.title.replaceAll('"',"'");a.style=`--animation-delay: ${100*this.count/1250}s`,a.classList.add(`${Fn}-${c}`),a.dataset.tooltip=t,a.dataset.swatchVariant=e.id,a.dataset.swatchVariantName=u,a.dataset.swatchImage=i,a.dataset.variant=e.id,a.style.setProperty("--swatch",h),l.href=(o=this.product.url,n=e.id,/variant=/.test(o)?o.replace(/(variant=)[^&]+/,"$1"+n):/\?/.test(o)?o.concat("&variant=").concat(n):o.concat("?variant=").concat(n)),l.dataset.swatch=t,l.disabled=!s,d.innerText=t,"limited"!=this.swatchesStyle?this.innerHTML+=r.innerHTML:this.count<=4&&(this.innerHTML+=r.innerHTML,this.limitedCount++),this.count++}var o,n,r;(this.swatchesCount++,this.swatchesCount==this.swatches.length)&&(null===(r=this.nativeScrollbar)||void 0===r||r.dispatchEvent(new Event("theme:swatches:loaded")))})),this.swatchCount=this.productInfo.querySelector(`[${Bn}]`),this.swatchElements=this.querySelectorAll(xn),this.swatchFieldset=this.productInfo.querySelector(kn),this.hideSwatchesTimer=0,this.swatchCount.hasAttribute(Bn)){if("text"==this.swatchesStyle||"text-slider"==this.swatchesStyle){if(this.swatchCount.innerText=`${this.count} ${this.count>1?theme.strings.otherColor:theme.strings.oneColor}`,"text"==this.swatchesStyle)return;this.swatchCount.addEventListener("mouseenter",(()=>{this.hideSwatchesTimer&&clearTimeout(this.hideSwatchesTimer),this.productInfo.classList.add(Dn),this.swatchFieldset.classList.add(Hn)})),this.productInfo.addEventListener("mouseleave",(()=>{this.hideSwatchesTimer=setTimeout((()=>{this.productInfo.classList.remove(Dn),this.swatchFieldset.classList.remove(Hn)}),100)}))}if("slider"!=this.swatchesStyle&&"grid"!=this.swatchesStyle||this.swatchFieldset.classList.add(Hn),"limited"==this.swatchesStyle){const t=this.count-this.limitedCount;this.swatchFieldset.classList.add(Hn),t>0&&(this.innerHTML+=`<div class="swatch-limited">+${t}</div>`)}}this.bindSwatchButtonEvents()}bindSwatchButtonEvents(){var t;null===(t=this.querySelectorAll(Mn))||void 0===t||t.forEach((t=>{t.addEventListener("mouseenter",this.showVariantImageEvent)})),this.productItem.addEventListener("mouseleave",this.productItemMouseLeaveEvent)}showVariantImage(t){var e;const s=null===(e=t.target.getAttribute(Wn))||void 0===e?void 0:e.replaceAll('"',"'"),i=this.productImage.querySelectorAll(`[${Nn}]`),o=this.productImage.querySelector(`[${Nn}="${s}"]`);null==i||i.forEach((t=>{t.classList.remove(Hn)})),null==o||o.classList.add(Hn)}hideVariantImages(){var t;null===(t=this.productImage.querySelectorAll(`[${Nn}].${Hn}`))||void 0===t||t.forEach((t=>{t.classList.remove(Hn)}))}getSwatchesJSON(){if(!this.hasAttribute(Rn))return{};const t=this.getAttribute(Rn).split(","),e={};return null==t||t.forEach((t=>{const[s,i]=t.split(":");e[s.trim()]=i.trim()})),e}constructor(){super(),this.productItemMouseLeaveEvent=()=>this.hideVariantImages(),this.showVariantImageEvent=t=>this.showVariantImage(t)}};const Vn=".flickity-prev-next-button",jn="[data-product-link]",zn="[data-hover-slide]",Jn="[data-hover-slide-touch]",Xn="[data-hover-slider]",Qn="video",Yn='[data-host="vimeo"]',Kn='[data-host="youtube"]';let Gn=class extends HTMLElement{connectedCallback(){window.theme.touch?this.initTouch():this.initFlickity()}disconnectedCallback(){this.flkty&&(this.flkty.options.watchCSS=!1,this.flkty.destroy()),this.removeEventListener("mouseenter",this.mouseEnterEvent),this.removeEventListener("mouseleave",this.mouseLeaveEvent)}initTouch(){this.style.setProperty("--slides-count",this.querySelectorAll(Jn).length),this.slider.addEventListener("scroll",this.handleScroll)}handleScroll(){const t=this.slider.scrollLeft/this.slider.clientWidth;this.style.setProperty("--slider-index",t)}initFlickity(){this.querySelectorAll(zn).length<2||(this.flkty=new window.theme.Flickity(this.slider,{cellSelector:zn,contain:!0,wrapAround:!0,watchCSS:!0,autoPlay:!1,draggable:!1,pageDots:!1,prevNextButtons:!0}),this.flkty.pausePlayer(),this.addEventListener("mouseenter",(()=>{this.flkty.unpausePlayer()})),this.addEventListener("mouseleave",(()=>{this.flkty.pausePlayer()})),this.closest(jn).addEventListener("click",(t=>{t.target.matches(Vn)&&t.preventDefault()})))}mouseEnterActions(){this.hovered=!0,this.videoActions()}mouseLeaveActions(){this.hovered=!1,this.videoActions()}videoActions(){const t=this.querySelector(Kn),e=this.querySelector(Yn),s=t||e,i=this.querySelector(Qn);if(s){let t=this.hovered?"playVideo":"pauseVideo",i=`{"event":"command","func":"${t}","args":""}`;e&&(t=this.hovered?"play":"pause",i=`{"method":"${t}"}`),s.contentWindow.postMessage(i,"*"),s.addEventListener("load",(t=>{this.videoActions()}))}else i&&(this.hovered?i.play():i.pause())}constructor(){super(),this.flkty=null,this.slider=this.querySelector(Xn),this.handleScroll=this.handleScroll.bind(this),this.hovered=!1,this.mouseEnterEvent=()=>this.mouseEnterActions(),this.mouseLeaveEvent=()=>this.mouseLeaveActions(),this.addEventListener("mouseenter",this.mouseEnterEvent),this.addEventListener("mouseleave",this.mouseLeaveEvent)}};const Zn={added:"is-added",animated:"is-animated",disabled:"is-disabled",error:"has-error",loading:"is-loading",open:"is-open",overlayText:"product-item--overlay-text",visible:"is-visible",siblingLinkCurrent:"sibling__link--current"},tr=3e3,er="[data-animation]",sr="[data-api-content]",ir="[data-quick-add-btn]",or="[data-add-to-cart]",nr='button, [href], select, textarea, [tabindex]:not([tabindex="-1"])',rr="[data-message-error]",ar="[data-quick-add-modal-handle]",lr="[data-product-upsell-ajax]",dr="[data-quick-add-modal-close]",cr="data-grid-item",hr="[data-product-information]",ur="[data-quick-add-holder]",pr="[data-quick-add-modal]",mr="[data-quick-add-modal-template]",vr="closing",gr="data-product-id",br="data-quick-add-modal-handle",wr="data-sibling-swapper",fr="data-quick-add-holder";let yr=class extends HTMLElement{connectedCallback(){this.modalButton&&this.modalButton.addEventListener("click",this.modalButtonClickEvent),this.buttonATC&&this.buttonATC.addEventListener("click",(t=>{t.preventDefault(),window.a11y.lastElement=this.buttonATC,document.dispatchEvent(new CustomEvent("theme:cart:add",{detail:{button:this.buttonATC}}))})),this.quickAddHolder&&(this.quickAddHolder.addEventListener("animationend",this.quickAddLoadingToggle),this.errorHandler())}modalButtonClickEvent(t){t.preventDefault();const e=this.modalButton.hasAttribute(wr),s=this.modalButton.classList.contains(Zn.siblingLinkCurrent);s||(this.modalButton.classList.add(Zn.loading),this.modalButton.disabled=!0,e&&!s&&(this.currentModal=t.target.closest(pr),this.currentModal.classList.add(Zn.loading)),this.renderModal())}modalCreate(t){const e=document.querySelector(`${pr}[${gr}="${this.productId}"]`);if(e)this.modal=e,this.modalOpen();else{const e=this.quickAddHolder.querySelector(mr);if(!e)return;const s=document.createElement("div");s.innerHTML=e.innerHTML,document.body.appendChild(s.querySelector(pr)),e.remove(),this.modal=document.querySelector(`${pr}[${gr}="${this.productId}"]`),this.modal.querySelector(lr).innerHTML=(new DOMParser).parseFromString(t,"text/html").querySelector(sr).innerHTML,this.modalCreatedCallback()}}modalOpen(){this.currentModal&&this.currentModal.dispatchEvent(new CustomEvent("theme:modal:close",{bubbles:!1})),"function"==typeof this.modal.show&&this.modal.show(),this.modal.setAttribute("open",!0),this.modal.removeAttribute("inert"),this.quickAddHolder.classList.add(Zn.disabled),this.modalButton&&(this.modalButton.classList.remove(Zn.loading),this.modalButton.disabled=!1,window.a11y.lastElement=this.modalButton),requestAnimationFrame((()=>{this.modal.querySelectorAll(er).forEach((t=>{t.classList.add(Zn.animated)}))})),document.dispatchEvent(new CustomEvent("theme:quick-add:open",{bubbles:!0})),document.dispatchEvent(new CustomEvent("theme:scroll:lock",{bubbles:!0})),document.addEventListener("theme:product:added",this.modalCloseOnProductAdded,{once:!0})}modalClose(){if(!this.isAnimating){if(!this.modal.hasAttribute(vr))return this.modal.setAttribute(vr,""),void(this.isAnimating=!0);"function"==typeof this.modal.close?this.modal.close():this.modal.removeAttribute("open"),this.modal.removeAttribute(vr),this.modal.setAttribute("inert",""),this.modal.classList.remove(Zn.loading),this.modalButton&&(this.modalButton.disabled=!1),this.quickAddHolder&&this.quickAddHolder.classList.contains(Zn.disabled)&&this.quickAddHolder.classList.remove(Zn.disabled),this.resetAnimatedItems(),window.theme.hasOpenModals()||document.dispatchEvent(new CustomEvent("theme:scroll:unlock",{bubbles:!0})),document.removeEventListener("theme:product:added",this.modalCloseOnProductAdded),this.a11y.removeTrapFocus(),this.a11y.autoFocusLastElement()}}modalEvents(){var t;null===(t=this.modal.querySelector(dr))||void 0===t||t.addEventListener("click",(t=>{t.preventDefault(),this.modalClose()})),this.modal.addEventListener("click",(t=>{"DIALOG"===t.target.nodeName&&"click"===t.type&&this.modalClose()})),this.modal.addEventListener("keydown",(t=>{"Escape"==t.code&&(t.preventDefault(),this.modalClose())})),this.modal.addEventListener("theme:modal:close",(()=>{this.modalClose()})),this.modal.addEventListener("animationend",(t=>{t.target===this.modal&&(this.isAnimating=!1,this.modal.hasAttribute(vr)?this.modalClose():setTimeout((()=>{this.a11y.trapFocus(this.modal);const t=this.modal.querySelector("[autofocus]")||this.modal.querySelector(nr);null==t||t.focus()}),50))}))}modalCloseOnProductAdded(){this.resetQuickAddButtons(),this.modal&&this.modal.hasAttribute("open")&&this.modalClose()}quickAddLoadingToggle(t){t.target==this.quickAddHolder&&this.quickAddHolder.classList.remove(Zn.disabled)}errorHandler(){this.quickAddHolder.addEventListener("theme:cart:error",(t=>{const e=t.detail.holder,s=e.closest(`[${cr}]`);if(!s)return;const i=e.querySelector(rr),o=s.classList.contains(Zn.overlayText),n=s.querySelector(hr),r=e.querySelector(or);r&&(r.classList.remove(Zn.added,Zn.loading),e.classList.add(Zn.error)),i&&(i.innerText=t.detail.description),o&&n.classList.add(Zn.hidden),setTimeout((()=>{this.resetQuickAddButtons(),o&&n.classList.remove(Zn.hidden)}),tr)}))}resetQuickAddButtons(){this.quickAddHolder&&this.quickAddHolder.classList.remove(Zn.visible,Zn.error),this.buttonQuickAdd&&(this.buttonQuickAdd.classList.remove(Zn.added),this.buttonQuickAdd.disabled=!1)}renderModal(){this.modal?this.modalOpen():window.fetch(`${window.theme.routes.root}products/${this.handle}?section_id=api-product-upsell`).then(this.upsellErrorsHandler).then((t=>t.text())).then((t=>{this.modalCreate(t)}))}modalCreatedCallback(){this.modalEvents(),this.modalOpen(),E(this.modal)}upsellErrorsHandler(t){return t.ok?t:t.json().then((function(e){throw new B({status:t.statusText,headers:t.headers,json:e})}))}resetAnimatedItems(){var t;null===(t=this.modal)||void 0===t||t.querySelectorAll(er).forEach((t=>{t.classList.remove(Zn.animated)}))}constructor(){var t;(super(),this.quickAddHolder=this.querySelector(ur),this.quickAddHolder)&&(this.modal=null,this.currentModal=null,this.productId=this.quickAddHolder.getAttribute(fr),this.modalButton=this.quickAddHolder.querySelector(ar),this.handle=null===(t=this.modalButton)||void 0===t?void 0:t.getAttribute(br),this.buttonQuickAdd=this.quickAddHolder.querySelector(ir),this.buttonATC=this.quickAddHolder.querySelector(or),this.button=this.modalButton||this.buttonATC,this.modalClose=this.modalClose.bind(this),this.modalCloseOnProductAdded=this.modalCloseOnProductAdded.bind(this),this.a11y=window.theme.a11y,this.isAnimating=!1,this.modalButtonClickEvent=this.modalButtonClickEvent.bind(this),this.quickAddLoadingToggle=this.quickAddLoadingToggle.bind(this))}};customElements.get("quick-add-product")||customElements.define("quick-add-product",yr),customElements.get("grid-swatch")||customElements.define("grid-swatch",Un),customElements.get("hover-images")||customElements.define("hover-images",Gn);const Lr="[data-button-arrow]",Er="[data-deferred-media-button]",Sr="model-viewer, video, iframe, button, [href], input, [tabindex]",Ar="[data-image-id]",kr="[data-product-media-list]",Cr="[data-section-type]",qr="slider__arrows",Tr="is-dragging",Mr="is-focused",xr="media--active",Ir="media--hidden",Or="media--hiding",Pr="data-active-media",Hr="data-button-prev",Dr="data-button-next",Fr="data-image-id",_r="data-media-id",$r="data-type",Br="data-fader-desktop",Wr="data-fader-mobile";function Nr(t){return t.replace(/http(s)?:/,"")}customElements.get("product-images")||customElements.define("product-images",class extends HTMLElement{connectedCallback(){1!==Object.keys(this.productMediaItems).length&&(this.productMediaObserver(),this.toggleEvents(),this.listen(),this.setHeight())}disconnectedCallback(){this.unlisten()}listen(){document.addEventListener("theme:resize:width",this.toggleEvents),document.addEventListener("theme:resize:width",this.setHeight),this.addEventListener("theme:media:select",this.selectMediaEvent)}unlisten(){document.removeEventListener("theme:resize:width",this.toggleEvents),document.removeEventListener("theme:resize:width",this.setHeight),this.removeEventListener("theme:media:select",this.selectMediaEvent)}toggleEvents(){const t=window.theme.isMobile();t&&this.hasAttribute(Wr)||!t&&this.hasAttribute(Br)?this.bindEventListeners():this.unbindEventListeners()}bindEventListeners(){this.initialized||(this.productMediaList.addEventListener("mousedown",this.handleMouseDown),this.productMediaList.addEventListener("mouseleave",this.handleMouseLeave),this.productMediaList.addEventListener("mouseup",this.handleMouseUp),this.productMediaList.addEventListener("mousemove",this.handleMouseMove),this.productMediaList.addEventListener("touchstart",this.handleMouseDown,{passive:!0}),this.productMediaList.addEventListener("touchend",this.handleMouseUp,{passive:!0}),this.productMediaList.addEventListener("touchmove",this.handleMouseMove,{passive:!0}),this.productMediaList.addEventListener("keyup",this.handleKeyUp),this.initArrows(),this.resetScrollPosition(),this.initialized=!0)}unbindEventListeners(){this.initialized&&(this.productMediaList.removeEventListener("mousedown",this.handleMouseDown),this.productMediaList.removeEventListener("mouseleave",this.handleMouseLeave),this.productMediaList.removeEventListener("mouseup",this.handleMouseUp),this.productMediaList.removeEventListener("mousemove",this.handleMouseMove),this.productMediaList.removeEventListener("touchstart",this.handleMouseDown),this.productMediaList.removeEventListener("touchend",this.handleMouseUp),this.productMediaList.removeEventListener("touchmove",this.handleMouseMove),this.productMediaList.removeEventListener("keyup",this.handleKeyUp),this.removeArrows(),this.initialized=!1)}handleMouseDown(t){this.isDown=!0,this.startX=(t.pageX||t.changedTouches[0].screenX)-this.offsetLeft,this.startY=(t.pageY||t.changedTouches[0].screenY)-this.offsetTop}handleMouseLeave(){this.isDown&&(this.isDown=!1)}handleMouseUp(t){const e=(t.pageX||t.changedTouches[0].screenX)-this.offsetLeft,s=(t.pageY||t.changedTouches[0].screenY)-this.offsetTop,i=e-this.startX,o=s-this.startY,n=i>0?1:-1,r=this.getCurrentMedia().hasAttribute($r)&&"image"===this.getCurrentMedia().getAttribute($r);Math.abs(i)>10&&Math.abs(i)>Math.abs(o)&&r&&(n<0?this.showNextImage():this.showPreviousImage()),this.isDown=!1,requestAnimationFrame((()=>{this.classList.remove(Tr)}))}handleMouseMove(){this.isDown&&this.classList.add(Tr)}handleKeyUp(t){"ArrowLeft"===t.code&&this.showPreviousImage(),"ArrowRight"===t.code&&this.showNextImage()}handleArrowsClickEvent(){var t;null===(t=this.querySelectorAll(Lr))||void 0===t||t.forEach((t=>{t.addEventListener("click",(t=>{t.preventDefault(),t.target.hasAttribute(Hr)&&this.showPreviousImage(),t.target.hasAttribute(Dr)&&this.showNextImage()}))}))}resetScrollPosition(){0!==this.productMediaList.scrollLeft&&(this.productMediaList.scrollLeft=0)}initArrows(){if(!this.buttons.length){const t=document.createElement("div");t.classList.add(qr),t.innerHTML=theme.sliderArrows.prev+theme.sliderArrows.next,this.productMediaList.append(t),this.buttons=this.querySelectorAll(Lr),this.buttonPrev=this.querySelector(`[${Hr}]`),this.buttonNext=this.querySelector(`[${Dr}]`)}this.handleArrowsClickEvent(),this.preloadImageOnArrowHover()}removeArrows(){var t;null===(t=this.querySelector(`.${qr}`))||void 0===t||t.remove()}preloadImageOnArrowHover(){var t,e;null===(t=this.buttonPrev)||void 0===t||t.addEventListener("mouseover",(()=>{const t=this.getPreviousMediaId();this.preloadImage(t)})),null===(e=this.buttonNext)||void 0===e||e.addEventListener("mouseover",(()=>{const t=this.getNextMediaId();this.preloadImage(t)}))}preloadImage(t){var e;null===(e=this.querySelector(`[${_r}="${t}"] img`))||void 0===e||e.setAttribute("loading","eager")}showMediaOnVariantSelect(t){const e=t.detail.id;this.setActiveMedia(e)}getCurrentMedia(){return this.querySelector(`${Ar}.${xr}`)}getNextMediaId(){const t=this.getCurrentMedia(),e=(null==t?void 0:t.nextElementSibling.hasAttribute(Fr))?null==t?void 0:t.nextElementSibling:this.querySelector(Ar);return null==e?void 0:e.getAttribute(_r)}getPreviousMediaId(){const t=this.getCurrentMedia(),e=this.productMediaItems.length-1,s=(null==t?void 0:t.previousElementSibling)||this.productMediaItems[e];return null==s?void 0:s.getAttribute(_r)}showNextImage(){const t=this.getNextMediaId();this.selectMedia(t)}showPreviousImage(){const t=this.getPreviousMediaId();this.selectMedia(t)}selectMedia(t){this.dispatchEvent(new CustomEvent("theme:media:select",{detail:{id:t}}))}setActiveMedia(t){if(!t)return;this.setAttribute(Pr,t);const e=this.querySelector(`${Ar}.${xr}`),s=this.querySelector(`[${_r}="${t}"]`),i=null==s?void 0:s.querySelector(Sr),o=s.querySelector("deferred-media");var n;(null==e||e.classList.add(Or),null==e||e.classList.remove(xr),null==s||s.classList.remove(Or,Ir),null==s||s.classList.add(xr),o&&!0!==o.getAttribute("loaded"))&&(null===(n=s.querySelector(Er))||void 0===n||n.dispatchEvent(new Event("click",{bubbles:!1})));requestAnimationFrame((()=>{this.setHeight(),document.body.classList.contains(Mr)&&(null==i||i.focus())}))}setHeight(){var t,e;const s=(null===(t=this.querySelector(`${Ar}.${xr}`))||void 0===t?void 0:t.offsetHeight)||(null===(e=this.productMediaItems[0])||void 0===e?void 0:e.offsetHeight);this.style.setProperty("--height",`${s}px`)}productMediaObserver(){this.productMediaItems.forEach((t=>{t.addEventListener("transitionend",(e=>{e.target==t&&t.classList.contains(Or)&&(t.classList.remove(Or),t.classList.add(Ir))})),t.addEventListener("transitioncancel",(e=>{e.target==t&&t.classList.contains(Or)&&(t.classList.remove(Or),t.classList.add(Ir))}))}))}constructor(){super(),this.initialized=!1,this.buttons=!1,this.isDown=!1,this.startX=0,this.startY=0,this.scrollLeft=0,this.onButtonArrowClick=t=>this.buttonArrowClickEvent(t),this.container=this.closest(Cr),this.handleMouseDown=this.handleMouseDown.bind(this),this.handleMouseLeave=this.handleMouseLeave.bind(this),this.handleMouseUp=this.handleMouseUp.bind(this),this.handleMouseMove=this.handleMouseMove.bind(this),this.handleKeyUp=this.handleKeyUp.bind(this),this.productMediaItems=this.querySelectorAll(Ar),this.productMediaList=this.querySelector(kr),this.setHeight=this.setHeight.bind(this),this.toggleEvents=this.toggleEvents.bind(this),this.selectMediaEvent=t=>this.showMediaOnVariantSelect(t)}});const Rr={productCutline:"[data-product-cutline]",productLink:"[data-product-link]",productGridItem:"[data-grid-item]",productInfo:"[data-product-information]",productImage:"[data-product-image-default]",productImageSibling:"[data-product-image-sibling]",productPrice:"[data-product-price]",siblingCount:"[data-sibling-count]",siblingFieldset:"[data-sibling-fieldset]",siblingLink:"[data-sibling-link]"},Ur="is-visible",Vr="is-fade",jr="no-events",zr="is-active",Jr="data-sibling-cutline",Xr="data-sibling-image",Qr="data-sibling-link",Yr="data-sibling-price",Kr="data-product-link";let Gr=class extends HTMLElement{connectedCallback(){this.product=this.closest(Rr.productGridItem),this.siblingCount=this.querySelector(Rr.siblingCount),this.siblingFieldset=this.querySelector(Rr.siblingFieldset),this.siblingLinks=this.querySelectorAll(Rr.siblingLink),this.productInfo=this.closest(Rr.productInfo),this.productLink=this.closest(Rr.link),this.hideSwatchesTimer=0,this.swatchesStyle=theme.settings.collectionSwatchStyle,this.siblingFieldset&&this.productInfo&&("grid"!=this.swatchesStyle&&"slider"!=this.swatchesStyle&&"limited"!=this.swatchesStyle||this.siblingFieldset.classList.add(Ur),this.siblingCount&&(this.siblingCount.addEventListener("mouseenter",(()=>this.showSiblings())),this.productInfo.addEventListener("mouseleave",(()=>this.hideSiblings())))),this.siblingLinks.length&&new class{init(){this.cacheDefaultValues(),this.product.addEventListener("mouseleave",(()=>this.resetProductValues())),this.swatches.forEach((t=>{t.addEventListener("mouseenter",(t=>this.showSibling(t)))})),this.productLinks.length&&this.swatches.forEach((t=>{t.addEventListener("click",(()=>{this.productLinks[0].click()}))}))}cacheDefaultValues(){this.productLinkValue=this.productLinks[0].hasAttribute(Kr)?this.productLinks[0].getAttribute(Kr):"",this.productPriceValue=this.productPrice.innerHTML,this.productCutline&&(this.productCutlineValue=this.productCutline.innerHTML)}resetProductValues(){this.product.classList.remove(zr),this.productLinkValue&&this.productLinks.forEach((t=>{t.href=this.productLinkValue})),this.productPrice&&(this.productPrice.innerHTML=this.productPriceValue),this.productCutline&&this.productCutline&&(this.productCutline.innerHTML=this.productCutlineValue,this.productCutline.title=this.productCutlineValue),this.hideSiblingImage()}showSibling(t){const e=t.target,s=e.hasAttribute(Qr)?e.getAttribute(Qr):"",i=e.hasAttribute(Yr)?e.getAttribute(Yr):"",o=e.hasAttribute(Jr)?e.getAttribute(Jr):"",n=e.hasAttribute(Xr)?e.getAttribute(Xr):"";s&&this.productLinks.forEach((t=>{t.href=s})),i&&(this.productPrice.innerHTML=`<span class="price">${i}</span>`),this.productCutline&&(o?(this.productCutline.innerHTML=o,this.productCutline.title=o):(this.productCutline.innerHTML="",this.productCutline.title="")),n&&this.showSiblingImage(n)}showSiblingImage(t){if(!this.productImageSibling)return;const e=window.devicePixelRatio||1,s=this.productImage.offsetWidth*e,i=function(t,e){if(null===e)return t;if("master"===e)return Nr(t);const s=t.match(/\.(jpg|jpeg|gif|png|bmp|bitmap|tiff|tif)(\?v=\d+)?$/i);if(s){const i=t.split(s[0]),o=s[0];return Nr(`${i[0]}_${e}${o}`)}return null}(t,180*Math.ceil(s/180)+"x"),o=this.productImageSibling.querySelector(`[src="${i}"]`),n=()=>{this.productImageSibling.classList.add(Ur),this.productImageSibling.querySelector(`[src="${i}"]`).classList.add(Vr)},r=()=>{this.productImageSibling.querySelectorAll("img").forEach((t=>{t.classList.remove(Vr)})),requestAnimationFrame(n)};if(o)r();else{const t=document.createElement("img");t.src=i,this.productCutline&&(t.alt=this.productCutline.innerText),t.addEventListener("load",(()=>{this.productImageSibling.append(t),r()}))}}hideSiblingImage(){this.productImageSibling&&(this.productImageSibling.classList.remove(Ur),this.productImageSibling.querySelectorAll("img").forEach((t=>{t.classList.remove(Vr)})))}constructor(t,e){this.swatches=t,this.product=e,this.productLinks=this.product.querySelectorAll(Rr.productLink),this.productCutline=this.product.querySelector(Rr.productCutline),this.productPrice=this.product.querySelector(Rr.productPrice),this.productImage=this.product.querySelector(Rr.productImage),this.productImageSibling=this.product.querySelector(Rr.productImageSibling),this.init()}}(this.siblingLinks,this.product)}showSiblings(){this.hideSwatchesTimer&&clearTimeout(this.hideSwatchesTimer),this.productLink&&this.productLink.classList.add(jr),"text"!=this.swatchesStyle&&this.siblingFieldset.classList.add(Ur)}hideSiblings(){this.hideSwatchesTimer=setTimeout((()=>{this.productLink&&this.productLink.classList.remove(jr),this.siblingFieldset.classList.remove(Ur)}),100)}constructor(){super()}};customElements.get("product-siblings")||customElements.define("product-siblings",Gr),Shopify.Products=function(){const t={howManyToShow:4,howManyToStoreInMemory:10,wrapperId:"recently-viewed-products",section:null,target:"api-product-grid-item",onComplete:null};let e=[],s=null,i=null;const o=new Date,n=new Date;n.setTime(o.getTime()+7776e6);const r={configuration:{expires:n.toGMTString(),path:"/",domain:window.location.hostname,sameSite:"none",secure:!0},name:"shopify_recently_viewed",write:function(t){const e=encodeURIComponent(t.join(" "));document.cookie=`${this.name}=${e}; expires=${this.configuration.expires}; path=${this.configuration.path}; domain=${this.configuration.domain}; sameSite=${this.configuration.sameSite}; secure=${this.configuration.secure}`},read:function(){let t=[],e=null;return-1!==document.cookie.indexOf("; ")&&document.cookie.split("; ").find((t=>t.startsWith(this.name)))&&(e=document.cookie.split("; ").find((t=>t.startsWith(this.name))).split("=")[1]),null!==e&&(t=decodeURIComponent(e).split(" ")),t},destroy:function(){document.cookie=`${this.name}=null; expires=${this.configuration.expires}; path=${this.configuration.path}; domain=${this.configuration.domain}`},remove:function(t){const e=this.read(),s=e.indexOf(t);-1!==s&&(e.splice(s,1),this.write(e))}},a=(e,s,o,n,l,d)=>{s.length&&e<d?fetch(`${window.theme.routes.root}products/${s[0]}?section_id=${l}`).then((t=>t.text())).then((t=>{const i=100*e,r=o.id?`#${o.id}`:"",c=document.createElement("div");let h=t;h=h.includes("||itemAnimationDelay||")?h.replaceAll("||itemAnimationDelay||",i):h,h=h.includes("||itemAnimationAnchor||")?h.replaceAll("||itemAnimationAnchor||",r):h,c.innerHTML=h,o.innerHTML+=c.querySelector("[data-api-content]").innerHTML,s.shift(),e++,a(e,s,o,n,l,d)})).catch((()=>{r.remove(s[0]),s.shift(),a(e,s,o,n,l,d)})):((e,s)=>{e.classList.remove("hidden");const o=r.read().length;if(Shopify.recentlyViewed&&i&&o&&o<i&&e.children.length){let t=[],s=[],i=0;for(const e in Shopify.recentlyViewed){i+=1;const o=Shopify.recentlyViewed[e].split(" "),n=parseInt(e.split("_")[1]);t=[...t,...o],(r.read().length===n||i===Object.keys(Shopify.recentlyViewed).length&&!s.length)&&(s=[...s,...o])}for(let i=0;i<e.children.length;i++){const o=e.children[i];t.length&&o.classList.remove(...t),s.length&&o.classList.add(...s)}}if(t.onComplete)try{t.onComplete(e,s)}catch(t){console.log(t)}})(o,n)};return{showRecentlyViewed:function(o){const n=o||{};Object.assign(t,n),e=r.read(),s=document.querySelector(`#${t.wrapperId}`),i=t.howManyToShow,t.howManyToShow=Math.min(e.length,t.howManyToShow),t.howManyToShow&&s&&a(0,e,s,t.section,t.target,i)},getConfig:function(){return t},clearList:function(){r.destroy()},recordRecentlyViewed:function(e){const s=e||{};Object.assign(t,s);let i=r.read();if(-1!==window.location.pathname.indexOf("/products/")){let e=decodeURIComponent(window.location.pathname).match(/\/products\/([a-z0-9\-]|[\u3000-\u303F]|[\u3040-\u309F]|[\u30A0-\u30FF]|[\uFF00-\uFFEF]|[\u4E00-\u9FAF]|[\u2605-\u2606]|[\u2190-\u2195]|[\u203B]|[\w\u0430-\u044f]|[\u0400-\u04FF]|[\u0900-\u097F]|[\u0590-\u05FF\u200f\u200e]|[\u0621-\u064A\u0660-\u0669 ])+/)[0].split("/products/")[1];t.handle&&(e=t.handle);const s=i.indexOf(e);-1===s?(i.unshift(e),i=i.splice(0,t.howManyToStoreInMemory)):(i.splice(s,1),i.unshift(e)),r.write(i)}},hasProducts:r.read().length>0}}();const Zr="[data-aos]",ta=".collection-item__image",ea="[data-column-image]",sa=".flickity-button.next",ia=".flickity-button.previous",oa="a:not(.btn)",na=".product-item__image",ra="[data-section-type]",aa="[data-slide]",la="[data-slider-thumb]",da="data-arrow-position-middle",ca="data-slide-index",ha="data-options",ua="data-slide-text-color",pa="aos-animate",ma="desktop",va="is-focused",ga="hidden",ba="is-initialized",wa="is-loading",fa="is-selected",ya="mobile",La="single-slide";customElements.get("slider-component")||customElements.define("slider-component",class extends HTMLElement{connectedCallback(){var t;if(this.slides.length<=1)return;this.hasAttribute(ha)&&(this.customOptions=JSON.parse(decodeURIComponent(this.getAttribute(ha)))),this.classList.add(wa);let e=aa;const s=!window.theme.isMobile(),i=`${aa}:not(.${ya})`,o=`${aa}:not(.${ma})`;(this.querySelectorAll(o).length||this.querySelectorAll(i).length)&&(e=s?i:o),this.querySelectorAll(e).length<=1&&(this.classList.add(La),this.classList.remove(wa)),this.sliderOptions={cellSelector:e,contain:!0,wrapAround:!0,adaptiveHeight:!0,...this.customOptions,on:{ready:()=>{requestAnimationFrame((()=>{this.classList.add(ba),this.classList.remove(wa),this.parentNode.dispatchEvent(new CustomEvent("theme:slider:loaded",{bubbles:!0,detail:{slider:this}}))})),this.slideActions(),this.sliderOptions.prevNextButtons&&this.positionArrows()},change:t=>{const e=this.slides[t];if(!e||this.sliderOptions.groupCells)return;const s=e.querySelectorAll(Zr);s.length&&s.forEach((t=>{t.classList.remove(pa),requestAnimationFrame((()=>{setTimeout((()=>{t.classList.add(pa)}),0)}))}))},resize:()=>{this.sliderOptions.prevNextButtons&&this.positionArrows()}}},this.initSlider(),this.flkty.on("change",(()=>this.slideActions(!0))),null===(t=this.thumbs)||void 0===t||t.forEach((t=>{t.addEventListener("click",(e=>{e.preventDefault();const s=[...t.parentElement.children].indexOf(t);this.flkty.select(s)}))})),this.flkty&&this.flkty.isActive||this.classList.remove(wa)}initSlider(){this.sliderOptions.fade?this.flkty=new window.theme.FlickityFade(this,this.sliderOptions):this.flkty=new window.theme.Flickity(this,this.sliderOptions)}bindEvents(){this.addEventListener("theme:slider:init",(()=>{this.initSlider()})),this.addEventListener("theme:slider:select",(t=>{this.flkty.selectCell(t.detail.index),this.flkty.stopPlayer()})),this.addEventListener("theme:slider:deselect",(()=>{this.flkty&&this.sliderOptions.hasOwnProperty("autoPlay")&&this.sliderOptions.autoPlay&&this.flkty.playPlayer()})),this.addEventListener("theme:slider:reposition",(()=>{var t;null===(t=this.flkty)||void 0===t||t.reposition()})),this.addEventListener("theme:slider:destroy",(()=>{var t;null===(t=this.flkty)||void 0===t||t.destroy()})),this.addEventListener("theme:slider:remove-slide",(t=>{var e,s;t.detail.slide&&(null===(e=this.flkty)||void 0===e||e.remove(t.detail.slide),0===(null===(s=this.flkty)||void 0===s?void 0:s.cells.length)&&this.section.classList.add(ga))}))}slideActions(t=!1){const e=this.querySelector(`.${fa}`);if(!e)return;const s=e.hasAttribute(ua)?e.getAttribute(ua):"",i=e.querySelector(oa),o=this.querySelectorAll(`${aa} a, ${aa} button`);if(document.body.classList.contains(va)&&i&&this.sliderOptions.groupCells&&t&&i.focus(),o.length&&o.forEach((t=>{const e=t.closest(aa);if(e){const s=e.classList.contains(fa)?0:-1;t.setAttribute("tabindex",s)}})),this.style.setProperty("--text",s),this.thumbs.length&&this.thumbs.length===this.slides.length&&e.hasAttribute(ca)){const t=parseInt(e.getAttribute(ca)),s=this.querySelector(`${la}.${fa}`);s&&s.classList.remove(fa),this.thumbs[t].classList.add(fa)}}positionArrows(){if(this.hasAttribute(da)&&this.sliderOptions.prevNextButtons){const t=this.querySelector(ta)||this.querySelector(na)||this.querySelector(ea);if(!t)return;this.querySelector(ia).style.top=t.clientHeight/2+"px",this.querySelector(sa).style.top=t.clientHeight/2+"px"}}disconnectedCallback(){this.flkty&&(this.flkty.options.watchCSS=!1,this.flkty.destroy())}constructor(){super(),this.flkty=null,this.slides=this.querySelectorAll(aa),this.thumbs=this.querySelectorAll(la),this.section=this.closest(ra),this.bindEvents()}});const Ea="[data-related-section]",Sa="[data-aos]",Aa="[data-tab]",ka=".tab-link__recent",Ca=".tab-content",qa="current",Ta="hidden",Ma="aos-animate",xa="aos-no-transition",Ia="is-focused",Oa="data-tab",Pa="data-tab-index";customElements.get("tabs-component")||customElements.define("tabs-component",class extends HTMLElement{connectedCallback(){const t=this.querySelectorAll(Aa);this.addEventListener("theme:tab:check",(()=>this.checkRecentTab())),this.addEventListener("theme:tab:hide",(()=>this.hideRelatedTab())),null==t||t.forEach((t=>{const e=parseInt(t.getAttribute(Oa)),s=this.querySelector(`${Ca}-${e}`);t.addEventListener("click",(()=>{this.tabChange(t,s)})),t.addEventListener("keyup",(e=>{"Space"!==e.code&&"Enter"!==e.code||!document.body.classList.contains(Ia)||this.tabChange(t,s)}))}))}tabChange(t,e){if(t.classList.contains(qa))return;const s=this.querySelector(`${Aa}.${qa}`),i=this.querySelector(`${Ca}.${qa}`);null==s||s.classList.remove(qa),null==i||i.classList.remove(qa),t.classList.add(qa),e.classList.add(qa),t.classList.contains(Ta)&&e.classList.add(Ta),this.a11y.a11y.removeTrapFocus(),this.dispatchEvent(new CustomEvent("theme:tab:change",{bubbles:!0})),t.dispatchEvent(new CustomEvent("theme:form:sticky",{bubbles:!0,detail:{element:"tab"}})),this.animateItems(e)}animateItems(t,e=!0){const s=t.querySelectorAll(Sa);s.length&&s.forEach((t=>{t.classList.remove(Ma),e&&(t.classList.add(xa),requestAnimationFrame((()=>{t.classList.remove(xa),t.classList.add(Ma)})))}))}checkRecentTab(){const t=this.querySelector(ka);if(t){t.classList.remove(Ta);const e=parseInt(t.getAttribute(Oa)),s=this.querySelector(`${Ca}[${Pa}="${e}"]`);s&&(s.classList.remove(Ta),this.animateItems(s,!1))}}hideRelatedTab(){const t=this.querySelector(Ea);if(!t)return;const e=t.closest(`${Ca}.${qa}`);if(!e)return;const s=parseInt(e.getAttribute(Pa)),i=this.querySelectorAll(Aa);if(i.length>s){const t=i[s].nextSibling;t&&(i[s].classList.add(Ta),t.dispatchEvent(new Event("click")))}}constructor(){super(),this.a11y=window.a11y}});const Ha="[data-actions]",Da="[data-content]",Fa="[data-button]",_a="data-height",$a="is-open",Ba="is-enabled";let Wa=class extends HTMLElement{connectedCallback(){this.setHeight(this.initialHeight),this.trigger.addEventListener("click",(()=>{this.setHeight(this.content.offsetHeight),this.classList.add($a)})),this.setHeight(this.initialHeight),this.toggleActions(),document.addEventListener("theme:resize",this.toggleActions),document.addEventListener("theme:collapsible:toggle",this.toggleActions)}disconnectedCallback(){document.removeEventListener("theme:resize",this.toggleActions),document.removeEventListener("theme:collapsible:toggle",this.toggleActions)}setHeight(t){this.style.setProperty("--height",`${t}px`)}toggleActions(){this.classList.toggle(Ba,this.content.offsetHeight+this.actions.offsetHeight>this.initialHeight)}constructor(){super(),this.initialHeight=this.getAttribute(_a),this.content=this.querySelector(Da),this.trigger=this.querySelector(Fa),this.actions=this.querySelector(Ha),this.toggleActions=this.toggleActions.bind(this)}};customElements.get("toggle-ellipsis")||customElements.define("toggle-ellipsis",Wa);const Na="[data-section-id]",Ra="data-tooltip",Ua="data-tooltip-stop-mouseenter",Va="tooltip-default",ja="is-visible",za="is-hiding";customElements.get("tooltip-component")||customElements.define("tooltip-component",class extends HTMLElement{connectedCallback(){if(!document.querySelector(`.${Va}`)){const t=`<div class="${Va}__arrow"></div><div class="${Va}__inner"><div class="${Va}__text"></div></div>`,e=document.createElement("div");e.className=Va,e.innerHTML=t,document.body.appendChild(e)}this.addEventListener("mouseenter",this.addPinMouseEvent),this.addEventListener("mouseleave",this.removePinMouseEvent),this.addEventListener("theme:tooltip:init",this.addPinEvent),document.addEventListener("theme:tooltip:close",this.removePinEvent)}addPin(t=!1){const e=document.querySelector(`.${Va}`),s=this.closest(Na),i=Array.from(s.classList).find((t=>t.startsWith("color-scheme-")));if(null==e||e.classList.add(i),this.label&&e&&(t&&!this.hasAttribute(Ua)||!t)){const t=e.querySelector(`.${Va}__arrow`),s=e.querySelector(`.${Va}__inner`);e.querySelector(`.${Va}__text`).innerHTML=this.label;const i=s.offsetWidth,o=this.getBoundingClientRect(),n=o.top,r=o.width,a=n+o.height+window.scrollY;let l=o.left-i/2+r/2;const d=24,c=l+i-window.theme.getWindowWidth()+d;c>0&&(l-=c),l<0&&(l=0),t.style.left=`${o.left+r/2}px`,e.style.setProperty("--tooltip-top",`${a}px`),s.style.transform=`translateX(${l}px)`,e.classList.remove(za),e.classList.add(ja),document.addEventListener("theme:scroll",this.removePinEvent)}}removePin(t,e=!1,s=!1){const i=document.querySelector(`.${Va}`),o=i.classList.contains(ja);i&&(e&&!this.hasAttribute(Ua)||!e)&&(o&&(s||t.detail.hideTransition)&&(i.classList.add(za),this.hideTransitionTimeout&&clearTimeout(this.hideTransitionTimeout),this.hideTransitionTimeout=setTimeout((()=>{i.classList.remove(za)}),this.transitionSpeed)),i.classList.remove(ja),document.removeEventListener("theme:scroll",this.removePinEvent))}disconnectedCallback(){this.removeEventListener("mouseenter",this.addPinMouseEvent),this.removeEventListener("mouseleave",this.removePinMouseEvent),this.removeEventListener("theme:tooltip:init",this.addPinEvent),document.removeEventListener("theme:tooltip:close",this.removePinEvent),document.removeEventListener("theme:scroll",this.removePinEvent)}constructor(){super(),this.label=this.hasAttribute(Ra)?this.getAttribute(Ra):"",this.transitionSpeed=200,this.hideTransitionTimeout=0,this.addPinEvent=()=>this.addPin(),this.addPinMouseEvent=()=>this.addPin(!0),this.removePinEvent=t=>window.theme.throttle(this.removePin(t),50),this.removePinMouseEvent=t=>this.removePin(t,!0,!0)}});const Ja={};function Xa(t={}){if(t.type||(t.type="json"),t.url)return Ja[t.url]?Ja[t.url]:function(t,e){const s=new Promise(((s,i)=>{"text"===e?fetch(t).then((t=>t.text())).then((t=>{s(t)})).catch((t=>{i(t)})):function(t,e,s){let i=document.getElementsByTagName("head")[0],o=!1,n=document.createElement("script");n.src=t,n.onload=n.onreadystatechange=function(){o||this.readyState&&"loaded"!=this.readyState&&"complete"!=this.readyState?s():(o=!0,e())},i.appendChild(n)}(t,(function(){s()}),(function(){i()}))}));return Ja[t]=s,s}(t.url,t.type);if(t.json)return Ja[t.json]?Promise.resolve(Ja[t.json]):window.fetch(t.json).then((t=>t.json())).then((e=>(Ja[t.json]=e,e)));if(t.name){const e="".concat(t.name,t.version);return Ja[e]?Ja[e]:function(t){const e="".concat(t.name,t.version),s=new Promise(((e,s)=>{try{window.Shopify.loadFeatures([{name:t.name,version:t.version,onLoad:t=>{!function(t,e,s){s?e(s):t()}(e,s,t)}}])}catch(t){s(t)}}));return Ja[e]=s,s}(t)}return Promise.reject()}document.addEventListener("DOMContentLoaded",(function(){const t=document.querySelector("[data-scroll-top-button]");t&&(t.addEventListener("click",(()=>{window.scrollTo({top:0,left:0,behavior:"smooth"})})),document.addEventListener("theme:scroll",(()=>{t.classList.toggle("is-visible",window.scrollY>window.innerHeight)}))),window.self!==window.top&&document.querySelector("html").classList.add("iframe"),"scrollBehavior"in document.documentElement.style||Xa({url:window.theme.assets.smoothscroll})})),window.navigator.cookieEnabled&&(document.documentElement.className=document.documentElement.className.replace("supports-no-cookies","supports-cookies"))}(themeVendor.ScrollLock);
