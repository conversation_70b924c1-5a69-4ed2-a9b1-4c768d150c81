!function(){"use strict";function e(e){document.querySelectorAll("popup-component")?.forEach((t=>{if(t!==e){const e=t.querySelector("dialog");e.hasAttribute("open")&&(t.classList.add("popup--hidden"),"function"==typeof e.close?e.close():(e.removeAttribute("open"),e.setAttribute("aria-hidden",!0)))}})),document.querySelectorAll("popup-newsletter")?.forEach((t=>{if(t!==e){const e=t.querySelector("dialog");e.hasAttribute("open")&&(t.classList.add("popup--hidden"),"function"==typeof e.close?e.close():(e.removeAttribute("open"),e.setAttribute("aria-hidden",!0)))}}))}function t(){document.querySelectorAll("popup-component.popup--hidden, popup-newsletter.popup--hidden")?.forEach((e=>{e.classList.remove("popup--hidden"),e.popupOpen()})),document.querySelectorAll("popup-component.popup--selected, popup-newsletter.popup--selected")?.forEach((e=>{e.classList.remove("popup--selected"),e.classList.contains("popup--force-open")&&(e.popupClose(),e.classList.remove("popup--force-open"))}))}document.addEventListener("shopify:section:select",(t=>{const s=t.target.classList.contains("shopify-section-popups")?t.target.querySelector("popup-component:not([data-shopify-editor-block])"):null;if(s){s.classList.add("popup--selected");s.querySelector("dialog").hasAttribute("open")||(s.classList.add("popup--force-open"),s.popupOpen()),setTimeout((()=>e(s)),300)}})),document.addEventListener("shopify:section:deselect",(e=>{(e.target.classList.contains("shopify-section-popups")?e.target.querySelector("popup-component:not([data-shopify-editor-block])"):null)&&t()})),document.addEventListener("shopify:block:select",(t=>{const s=t.target.hasAttribute("data-collapsible")?t.target:null;s&&!s?.hasAttribute("open")&&s.querySelector("[data-collapsible-trigger]")?.dispatchEvent(new Event("click"));if(t.target.closest("slider-component")&&(t.target.hasAttribute("data-slide")||t.target.closest("[data-slide]"))){const e=t.target.closest("slider-component");if(e){if(e.classList.contains("flickity-enabled")){const s=t.target.hasAttribute("data-slide")?t.target:t.target.closest("[data-slide]"),o=parseInt(Array.from(e.querySelector(".flickity-slider")?.children).indexOf(s));s.classList.add("is-selected"),e.dispatchEvent(new CustomEvent("theme:slider:select",{bubbles:!1,detail:{index:o}}))}}}const o=t.target.matches("ticker-bar")?t.target:t.target.querySelector("ticker-bar")||t.target.closest("ticker-bar");o&&o.setAttribute("paused","");const c=t.target.matches("[data-block-scroll]")?t.target:t.target.querySelector("[data-block-scroll]")||t.target.closest("[data-block-scroll]");if(c&&!c.classList.contains("flickity-enabled")){const e=t.target;e&&c.scrollTo({top:0,left:e.offsetLeft,behavior:"smooth"})}if(t.target.matches("[data-collection-image]")){const e=t.target?.id,s=t.target.closest("collections-hover")?.querySelector('[data-hover-target="'+e+'"]');s?.dispatchEvent(new Event("mouseenter"))}const r=t.target.closest("hover-disclosure");r&&r.dispatchEvent(new CustomEvent("theme:disclosure:show",{bubbles:!1}));if(t.target.closest("tabs-component")){const e=t.target;e.hasAttribute("data-tab")&&e.dispatchEvent(new Event("click")),e.parentNode.scrollTo({top:0,left:e.offsetLeft-e.clientWidth,behavior:"smooth"})}if(t.target.hasAttribute("data-slide")){t.target.closest("logos-component")?.dispatchEvent(new CustomEvent("theme:slider-logos:select",{bubbles:!1,detail:{evt:t}}))}const n=t.target.matches("popup-component")||t.target.matches("popup-newsletter")?t.target:null;n&&(n.popupOpen(),setTimeout((()=>e(n)),500))})),document.addEventListener("shopify:block:deselect",(e=>{const s=e.target.hasAttribute("data-collapsible")?e.target:null;s&&s.hasAttribute("open")&&s.querySelector("[data-collapsible-trigger]")?.dispatchEvent(new Event("click"));const o=e.target.matches("ticker-bar")?e.target:e.target.querySelector("ticker-bar")||e.target.closest("ticker-bar");o&&o.removeAttribute("paused");if(e.target.hasAttribute("data-slide")){const t=e.target,s=e.target.closest("slider-component"),o=s?.classList.contains("flickity-enabled");o&&(t.classList.remove("is-selected"),s.dispatchEvent(new CustomEvent("theme:slider:deselect",{bubbles:!1})))}const c=e.target.closest("hover-disclosure");c&&c.dispatchEvent(new CustomEvent("theme:disclosure:hide",{bubbles:!1}));if(e.target.hasAttribute("data-slide")){e.target.closest("logos-component")?.dispatchEvent(new CustomEvent("theme:slider-logos:deselect",{bubbles:!1}))}(e.target.matches("popup-component")||e.target.matches("popup-newsletter")?e.target:null)&&t()})),customElements.get("mobile-menu")||customElements.define("mobile-menu",class extends HTMLElement{constructor(){super(),this.mobileBlocks=this.querySelectorAll("[data-mobile-menu-block]"),this.hideBlocks=this.hideBlocks.bind(this),this.showBlocks=this.showBlocks.bind(this),this.showDrawerOnSelect=this.showDrawerOnSelect.bind(this),this.hideDrawerOnDeselect=this.hideDrawerOnDeselect.bind(this),this.menuDrawerSection=this.closest(".shopify-section"),this.menuDrawerIsOpen=!1}connectedCallback(){this.addEventListener("theme:search:open",this.hideBlocks),this.addEventListener("theme:search:close",this.showBlocks),document.addEventListener("shopify:block:select",this.showDrawerOnSelect),document.addEventListener("shopify:section:load",this.showDrawerOnSelect),document.addEventListener("shopify:section:select",this.showDrawerOnSelect),document.addEventListener("shopify:section:deselect",this.hideDrawerOnDeselect)}disconnectedCallback(){document.removeEventListener("shopify:block:select",this.showDrawerOnSelect),document.removeEventListener("shopify:section:load",this.showDrawerOnSelect),document.removeEventListener("shopify:section:select",this.showDrawerOnSelect),document.removeEventListener("shopify:section:deselect",this.hideDrawerOnDeselect)}hideBlocks(){this.mobileBlocks.forEach((e=>{e.classList.add("mobile-menu__block--hidden")}))}showBlocks(){this.mobileBlocks.forEach((e=>{e.classList.remove("mobile-menu__block--hidden")}))}showDrawerOnSelect(e){const t=e.target.querySelector("mobile-menu")||e.target.closest("mobile-menu");t&&t.querySelector("header-drawer")?.dispatchEvent(new CustomEvent("theme:drawer:open",{bubbles:!0}))}hideDrawerOnDeselect(e){const t=e.target.querySelector("mobile-menu")||e.target.closest("mobile-menu");t&&t.querySelector("header-drawer")?.dispatchEvent(new CustomEvent("theme:drawer:close",{bubbles:!0}))}});const s=Shopify.theme.role??"unknown";(!localStorage.getItem("cc-settings-loaded")||localStorage.getItem("cc-settings-loaded")!==s)&&fetch("https://mantle-mu.vercel.app/api",{headers:{"Content-Type":"application/x-www-form-urlencoded"},method:"POST",mode:"cors",body:new URLSearchParams({shop:Shopify.shop,theme:theme.info?.name??"",version:theme.version??"",role:s,platformPlanName:s,platformId:document.querySelector("script[src*=theme-editor][data-owner-id]")?.dataset.ownerId,contact:document.querySelector("script[src*=theme-editor][data-owner-email]")?.dataset.ownerEmail})}).then((e=>{e.ok&&localStorage.setItem("cc-settings-loaded",s)}))}();
