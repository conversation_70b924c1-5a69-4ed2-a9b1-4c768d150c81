!function(){"use strict";const e={marquee:".announcement__bar-holder--marquee",slide:"[data-slide]",slider:"[data-slider]",ticker:"ticker-bar",tickerSlide:".announcement__slide"};customElements.get("announcement-bar")||customElements.define("announcement-bar",class extends HTMLElement{constructor(){super(),this.slider=this.querySelector(e.slider),this.enableSlider=!window.theme.isMobile(),this.slidesCount=this.querySelectorAll(e.tickerSlide).length,this.initSliderEvent=e=>this.initSlider(e)}connectedCallback(){this.slider&&this.initSliders(),this.addEventListener("theme:countdown:hide",(t=>{if(window.Shopify.designMode)return;const i=t.target.closest(e.marquee);if(1===this.slidesCount){this.querySelector(e.ticker).style.display="none"}if(i){const i=t.target.closest(e.tickerSlide);this.removeTickerText(i)}else{const i=t.target.closest(e.slide);this.removeSlide(i)}})),this.addEventListener("theme:countdown:expire",(()=>{this.querySelectorAll(e.ticker)?.forEach((e=>{e.dispatchEvent(new CustomEvent("theme:ticker:refresh"))}))})),document.dispatchEvent(new CustomEvent("theme:announcement:init",{bubbles:!0}))}initSliders(){this.initSlider(),document.addEventListener("theme:resize:width",this.initSliderEvent),this.addEventListener("theme:slider:loaded",(()=>{this.querySelectorAll(e.tickerBar)?.forEach((e=>{e.dispatchEvent(new CustomEvent("theme:ticker:refresh"))}))}))}initSlider(){const e=!window.theme.isMobile(),t=!e;(e&&this.enableSlider||t&&!this.enableSlider)&&(this.slider.dispatchEvent(new CustomEvent("theme:slider:destroy",{bubbles:!1})),e&&this.enableSlider?this.enableSlider=!1:t&&!this.enableSlider&&(this.enableSlider=!0),this.slider.dispatchEvent(new CustomEvent("theme:slider:init",{bubbles:!1})),this.slider.dispatchEvent(new CustomEvent("theme:slider:reposition",{bubbles:!1})))}removeSlide(e){this.slider.dispatchEvent(new CustomEvent("theme:slider:remove-slide",{bubbles:!1,detail:{slide:e}}))}removeTickerText(t){const i=t.closest(e.ticker);t.remove(),i.dispatchEvent(new CustomEvent("theme:ticker:refresh"))}disconnectedCallback(){document.removeEventListener("theme:resize:width",this.initSliderEvent),document.removeEventListener("theme:resize:width",this.tickerResizeEvent)}})}();
