<!-- /snippets/social-icon.liquid -->

{% comment %}
    Renders a social icon because render can't accept strings as variables

    Accepts:
    - filename: {String} name of icon

    Usage:
    {% render 'header-icon', filename: 'icon-clean-account' %}
{% endcomment %}


{%- case filename -%}
  {%- when 'icon-add-bag' -%}
    {%- render 'icon-add-bag' -%}
  {%- when 'icon-add-cart' -%}
    {%- render 'icon-add-cart' -%}
  {%- when 'icon-cart' -%}
    {%- render 'icon-cart' -%}
  {%- when 'icon-bag' -%}
    {%- render 'icon-bag' -%}
{%- endcase -%}

