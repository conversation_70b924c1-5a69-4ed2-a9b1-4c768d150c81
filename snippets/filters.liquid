{%- liquid
  assign filters_default = search.filters | default: collection.filters
  assign default_open = section.settings.default_open

  capture swatch_button_style
    case settings.swatch_style
      when 'square'
        echo 'swatch__button--square'
      when 'circle'
        echo 'swatch__button--circle'
    endcase
  endcapture
-%}

<collapsible-elements>
  <form data-collection-filters-form class="collection__filters" id="{{ section.id }}-form-filter">
    {%- if template.name == 'search' and search.terms -%}
      <input type="hidden" name="q" value="{{ search.terms | escape }}">
      <input name="options[prefix]" type="hidden" value="last">
    {%- endif -%}

    {%- unless section.settings.collection_linklist == blank or section.settings.collection_linklist.empty? -%}
      {%- liquid
        assign custom_nav_list = section.settings.collection_linklist | handleize
        assign animation_delay = animation_delay | plus: animation_delay_increment
      -%}

      <div
        class="filter-group"
        data-animation="{{ animation_name }}"
        data-animation-delay="{{ animation_delay }}"
        data-animation-duration="{{ animation_duration }}"
        {{ block.shopify_attributes }}
      >
        <details
          data-collapsible
          {% if default_open %}
            open="true"
          {% endif %}
        >
          <summary class="filter-group__heading" data-collapsible-trigger>
            <span>{{ linklists[custom_nav_list].title }}</span>

            {%- render 'icon-plus' -%}
            {%- render 'icon-minus' -%}
          </summary>

          <div
            class="filter-group__body"
            data-collapsible-body
            {% if default_open %}
              style="height: auto;"
            {% endif %}
          >
            <div class="filter-group__content" data-collapsible-content>
              <ul class="collection-nav">
                {%- for link in linklists[custom_nav_list].links -%}
                  {%- if link.links == blank -%}
                    <li
                      class="sidebar__item {% if link.active %}sidebar__item--active link--remove{% else %}link--add{% endif %}{% if forloop.index > 10 %} hidden{% endif %}"
                      {% if forloop.index > 10 %}
                        data-link-hidden
                      {% endif %}
                    >
                      <a href="{{ link.url }}">{{ link.title }}</a>
                    </li>
                  {%- endif -%}
                {%- endfor -%}
              </ul>

              {%- if linklists[custom_nav_list].links.size > 10 -%}
                <div class="collection__sidebar__actions">
                  <a href="#" data-show-more class="collection__sidebar__link">
                    {%- render 'icon-arrow-separate-vertical' -%}
                    <span>{{ 'collections.general.show_more' | t }}</span>
                  </a>
                </div>
              {%- endif -%}
            </div>
          </div>
        </details>
      </div>
    {%- endunless -%}

    {%- for filter in filters_default -%}
      {%- liquid
        assign filter_check_active = filter.values | where: 'active', false
        assign filter_check_count = filter.values | where: 'count', 0
        assign filter_hidden = false

        if filter.type == 'list' and filter.values.size == filter_check_active.size and filter.values.size == filter_check_count.size
          assign filter_hidden = true
        endif

        assign animation_delay = animation_delay | plus: animation_delay_increment

        if forloop.first and animation_delay == 50
          assign animation_delay = animation_delay | plus: 200
        endif
      -%}

      <div
        class="filter-group{% if filter_hidden %} filter-group--hidden{% endif %}"
        data-animation="{{ animation_name }}"
        data-animation-duration="{{ animation_duration }}"
        {% if filter_hidden %}
          style="display: none;"
          {% assign animation_delay = animation_delay | minus: animation_delay_increment %}
        {% endif %}
        data-animation-delay="{{ animation_delay }}"
      >
        <details
          data-collapsible
          {% if default_open %}
            open="true"
          {% endif %}
        >
          <summary class="filter-group__heading" data-collapsible-trigger>
            <span>{{ filter.label }} </span>
            {%- if filter.active_values.size > 0 -%}
              <span>({{ filter.active_values.size }})</span>
            {%- endif -%}

            {%- render 'icon-plus' -%}
            {%- render 'icon-minus' -%}
          </summary>

          <div
            class="filter-group__body"
            data-collapsible-body
            {% if default_open %}
              style="height: auto;"
            {% endif %}
          >
            <div class="filter-group__content" data-collapsible-content>
              {% comment %} Determine if current option matches swatch handle translations {% endcomment %}
              {%- liquid
                assign option_name_handle_separator = filter.label | handle | prepend: ',' | append: ','
                assign is_swatch_option = false
                assign is_native_swatch_option = false

                if settings.swatches_type == 'theme'
                  assign swatch_translation = 'general.swatches.color' | t
                  assign translation_string = swatch_translation | remove: '  ' | replace: ', ', ',' | replace: ' ,', ',' | replace: ',', '____' | handle | replace: '____', ',' | append: ',' | prepend: ','

                  if translation_string contains option_name_handle_separator
                    assign is_swatch_option = true
                  endif
                endif

                if settings.swatches_type == 'native'
                  for value in option.values
                    if value.swatch
                      assign is_native_swatch_option = true
                      break
                    endif
                  endfor
                endif
              -%}

              {%- case filter.type -%}
                {%- when 'list', 'boolean' -%}
                  <ul class="collection-nav{% if filter.presentation == "image" %} collection-nav--image-grid{% endif %}">
                    {%- for filter_value in filter.values -%}
                      <li
                        class="sidebar__item{% if filter_value.active %} sidebar__item--active link--remove{% elsif filter_value.count == 0 and filter_value.active == false %} link--disable{% else %} link--add{% endif %}{% if forloop.index > 10 %} hidden{% endif %}"
                        {% if forloop.index > 10 %}
                          data-link-hidden
                        {% endif %}
                      >
                        <input
                          type="checkbox"
                          name="{{ filter_value.param_name }}"
                          value="{{ filter_value.value }}"
                          id="filter-{{ filter.label }}-{{ forloop.index }}"
                          {% if filter_value.active -%}
                            checked
                          {%- endif %}
                          {% if filter_value.count == 0 and filter_value.active == false -%}
                            disabled
                          {%- endif %}
                        >
                        <label for="filter-{{ filter.label }}-{{ forloop.index }}">
                          {%- if is_swatch_option and filter.presentation != 'swatch' -%}
                            <span
                              class="swatches swatch__button {{ swatch_button_style }} swatch-{{ filter_value.label | handle }}"
                              data-swatch="{{ filter_value.label | escape_once }}"
                              style="--swatch: var(--{{ filter_value.label | handle }});"
                            ></span>

                          {%- elsif filter.presentation == 'swatch' -%}
                            {%- liquid
                              assign empty_swatch = false
                              if filter_value.swatch.color == blank and filter_value.swatch.image == blank
                                assign empty_swatch = true
                              endif
                            -%}

                            <div class="shopify-swatch">
                              {%- if settings.swatches_type != 'disabled' -%}
                                {%- if filter_value.swatch.image != blank -%}
                                  <div class="swatch__button {{ swatch_button_style }}">
                                    {%- render 'image',
                                      image: filter_value.swatch.image,
                                      width: 24,
                                      height: 24,
                                      sizes: '48px',
                                      widths: '24, 36, 48, 60, 72',
                                      cover: true
                                    -%}
                                  </div>
                                {%- elsif filter_value.swatch.color != blank -%}
                                  {%- liquid
                                    assign is_white = false
                                    if filter_value.swatch.color == '#ffffff'
                                      assign is_white = true
                                    endif
                                  -%}

                                  <div
                                    class="swatch__button {{ swatch_button_style }}"
                                    {% if is_white %}
                                      data-swatch="white"
                                    {% endif %}
                                    style="background-color: {{ filter_value.swatch.color }};"
                                  ></div>
                                {%- else -%}
                                  <div
                                    class="swatch__button {{ swatch_button_style }}{% if empty_swatch %} swatch__button--empty{% endif %}"
                                  ></div>
                                {%- endif -%}
                              {%- endif -%}
                            </div>
                          {%- elsif filter.presentation == 'image' -%}
                            {%- capture sizes -%}
                                (min-width: 990px) 40px, (min-width: 750px) 43px, calc((100vw - 96px) / 3)
                              {%- endcapture -%}

                            {%- render 'image',
                              image: filter_value.image.preview_image,
                              height: 100,
                              sizes: sizes,
                              widths: '50, 100, 150, 200, 250, 300'
                            -%}
                          {%- endif -%}

                          <span>{{ filter_value.label }}</span>
                        </label>
                      </li>
                    {%- endfor -%}
                  </ul>

                  {%- if filter.values.size > 10 -%}
                    <div class="collection__sidebar__actions">
                      <a href="#" data-show-more class="collection__sidebar__link">
                        {%- render 'icon-arrow-separate-vertical' -%}
                        <span>{{ 'collections.general.show_more' | t }}</span>
                      </a>
                    </div>
                  {%- endif -%}
                {%- when 'price_range' -%}
                  <div class="filter__price" data-range-holder>
                    {%- liquid
                      if shop.money_format contains 'comma_separator'
                        assign filter_max_money = filter.range_max | money_without_currency | replace: '.', '' | replace: ',', '.'
                      elsif shop.money_format contains 'space_separator'
                        assign filter_max_money = filter.range_max | money_without_currency | replace: ' ', '' | replace: ',', '.'
                      elsif shop.money_format contains 'apostrophe_separator'
                        assign filter_max_money = filter.range_max | money_without_currency | replace: "'", ''
                      else
                        assign filter_max_money = filter.range_max | money_without_currency | replace: ',', ''
                      endif
                      assign filter_max_money_ceil = filter_max_money | ceil
                      assign filter_min_value = 0
                      assign filter_max_value = filter_max_money_ceil

                      if filter.min_value.value
                        if shop.money_format contains 'comma_separator'
                          assign filter_min_value = filter.min_value.value | money_without_currency | replace: '.', '' | replace: ',', '.' | floor
                        elsif shop.money_format contains 'space_separator'
                          assign filter_min_value = filter.min_value.value | money_without_currency | replace: ' ', '' | replace: ',', '.' | floor
                        elsif shop.money_format contains 'apostrophe_separator'
                          assign filter_min_value = filter.min_value.value | money_without_currency | replace: "'", '' | floor
                        else
                          assign filter_min_value = filter.min_value.value | money_without_currency | replace: ',', '' | floor
                        endif
                      endif

                      if filter.max_value.value
                        if shop.money_format contains 'comma_separator'
                          assign filter_max_value = filter.max_value.value | money_without_currency | replace: '.', '' | replace: ',', '.' | ceil
                        elsif shop.money_format contains 'space_separator'
                          assign filter_max_value = filter.max_value.value | money_without_currency | replace: ' ', '' | replace: ',', '.' | ceil
                        elsif shop.money_format contains 'apostrophe_separator'
                          assign filter_max_value = filter.max_value.value | money_without_currency | replace: "'", '' | ceil
                        else
                          assign filter_max_value = filter.max_value.value | money_without_currency | replace: ',', '' | ceil
                        endif
                      endif
                    -%}

                    <range-slider
                      class="filter__price__range range"
                      data-range-slider
                      data-range-filter-update
                      data-se-min="0"
                      data-se-step="1"
                      data-se-min-value="{{ filter_min_value }}"
                      data-se-max-value="{{ filter_max_value }}"
                      data-se-max="{{ filter_max_money_ceil }}"
                    >
                      <div class="range__dot range__dot--left" data-range-left>
                        <span>&nbsp;</span>
                      </div>
                      <div class="range__dot range__dot--right" data-range-right>
                        <span>&nbsp;</span>
                      </div>
                      <div class="range__line">
                        <span data-range-line>&nbsp;</span>
                      </div>
                    </range-slider>

                    <script src="{{ 'range-slider.js' | asset_url }}" defer="defer"></script>

                    <div class="filter__price__fields">
                      <div class="filter__price__field filter__price__from">
                        <span class="filter__price__currency">{{ cart.currency.symbol }}</span>

                        <input
                          data-field-price-min
                          class="filter__price__input"
                          name="{{ filter.min_value.param_name }}"
                          id="filter-price-from-{{ filter.label }}-{{ forloop.index }}"
                          {% if filter.min_value.value %}
                            value="{{ filter_min_value }}"
                          {% endif %}
                          type="number"
                          placeholder="{{ filter_min_value }}"
                          min="0"
                          max="{{ filter_max_money }}"
                        >

                        <label for="filter-price-from-{{ filter.label }}-{{ forloop.index }}">
                          {{- 'products.general.from' | t -}}
                        </label>
                      </div>
                      <div class="filter__price__spacer">-</div>
                      <div class="filter__price__field filter__price__to">
                        <span class="filter__price__currency">{{ cart.currency.symbol }}</span>

                        <input
                          data-field-price-max
                          class="filter__price__input"
                          name="{{ filter.max_value.param_name }}"
                          id="filter-price-to-{{ filter.label }}-{{ forloop.index }}"
                          {% if filter.max_value.value %}
                            value="{{ filter_max_value }}"
                          {% endif %}
                          type="number"
                          placeholder="{{ filter_max_value }}"
                          min="0"
                          max="{{ filter_max_money_ceil }}"
                        >

                        <label for="filter-price-to-{{ filter.label }}-{{ forloop.index }}">
                          {{- 'products.general.to' | t -}}
                        </label>
                      </div>
                    </div>
                  </div>
              {%- endcase -%}
            </div>
          </div>
        </details>
      </div>
    {%- endfor -%}

    <noscript>
      <button
        type="submit"
        class="collection__filters__btn btn btn--primary btn--outline btn--full"
        aria-label="{{ 'collections.general.filters' | t }}"
      >
        <span>{{ 'collections.general.filters' | t }}</span>
      </button>
    </noscript>
  </form>
</collapsible-elements>
