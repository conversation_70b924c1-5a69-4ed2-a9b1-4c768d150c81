<!-- /snippets/product-siblings.liquid -->

{%- liquid
  assign metafields = metafields | default: false
  assign show_siblings = false
  assign sibs_collection = collections[block.settings.siblings_collection].products
  assign sibling_color = block.settings.sibling_color | default: product.metafields.theme.sibling_color.value | default: product.metafields.theme.sibling_colour.value | default: product.metafields.theme.siblings_color.value | default: product.metafields.theme.siblings_colour.value | default: product.metafields.theme.siblings_colors.value | default: product.metafields.theme.siblings_colours.value
  assign hide_labels_class = ''

  if metafields
    assign sibs_collection = product.metafields.theme.sibling_products.value | default: collections[product.metafields.theme.siblings.value].products
  endif

  if metafields and sibs_collection != blank
    assign show_siblings = true
  elsif metafields == false and sibs_collection.size > 0
    assign show_siblings = true
  endif

  if settings.show_labels == false
    assign hide_labels_class = ' variant__labels--hide'
  endif
-%}

{%- if show_siblings -%}
  <div
    class="product__block{% if settings.show_lines %} product__block--lines{% endif %} product__siblings{{ hide_labels_class }} block-padding"
    {{ block_style }}
    {% if animation_name %}
      data-animation="{{ animation_name }}"
      data-animation-duration="{{ animation_duration }}"
      data-animation-delay="{{ animation_delay }}"
    {% endif %}
    {{ block.shopify_attributes }}
  >
    {% comment %} Form ID must match value in product-form.liquid {% endcomment %}
    <input
      type="hidden"
      form="{{ product_form_id }}"
      name="properties[{{ 'general.siblings.label' | t }}]"
      value="{{ sibling_color }}"
    >

    {%- if sibs_collection -%}
      <fieldset>
        <div class="radio__fieldset radio__fieldset--swatches">
          <legend class="radio__legend">
            <span class="radio__legend__label">
              <span class="radio__legend__option-name">{{ 'general.siblings.label' | t }}</span>

              {%- if sibling_color != blank -%}
                <small class="radio__legend__value">{{ sibling_color }}</small>
              {%- endif -%}
            </span>
          </legend>

          <div class="radio__buttons">
            {%- for sib_product in sibs_collection limit: 50 -%}
              {%- liquid
                assign modifier_class = ''
                if sib_product.handle == product.handle
                  assign modifier_class = ' sibling__link--current'
                endif

                if sib_product.available == false
                  assign modifier_class = modifier_class | append: ' sibling__link--sold-out'
                endif

                assign sib_color = sib_product.metafields.theme.sibling_color.value | default: sib_product.metafields.theme.sibling_colour.value | default: sib_product.metafields.theme.siblings_color.value | default: sib_product.metafields.theme.siblings_colour.value | default: sib_product.metafields.theme.siblings_colors.value | default: sib_product.metafields.theme.siblings_colours.value
                assign swatch_color = sib_color | handle

                capture swatch_image
                  if settings.sibling_style == 'image'
                    case settings.swatch_size
                      when 'regular'
                        assign sibling_width = 2.2 | times: settings.base_font_size | round
                      when 'large'
                        assign sibling_width = 3 | times: settings.base_font_size | round
                    endcase

                    assign sibling_sizes = sibling_width | append: 'px'
                    assign sibling_width_15x = sibling_width | times: 1.5
                    assign sibling_width_2x = sibling_width | times: 2
                    assign sibling_widths = sibling_width | append: ', ' | append: sibling_width_15x | append: ', ' | append: sibling_width_2x

                    render 'image', image: sib_product.featured_media.preview_image, width: sibling_width_2x, height: sibling_width_2x, widths: sibling_widths, sizes: sibling_sizes, aspect_ratio: 1, show_backfill: false
                  endif
                endcapture
              -%}

              <tooltip-component
                class="swatches swatch__button swatch__button--{{ settings.swatch_style }} swatch-{{ swatch_color | handle }}"
                style="--swatch: var(--{{ swatch_color }});"
                data-tooltip="{{ sib_color }}"
              >
                {%- if product_api -%}
                  {%- assign unique = section.id | append: '-' | append: sib_product.id -%}

                  <quick-add-product>
                    <div data-quick-add-holder="{{ sib_product.id }}">
                      <a
                        href="{{ sib_product.url }}"
                        class="sibling__link{{ modifier_class }}"
                        data-quick-add-btn
                        data-sibling-swapper
                        data-quick-add-modal-handle="{{ sib_product.handle }}"
                      >
                        {{ swatch_image }}
                        <span class="visually-hidden">{{ swatch_color }}</span>
                      </a>

                      {%- render 'product-quick-add-modal-template', product_id: sib_product.id, unique: unique -%}
                    </div>
                  </quick-add-product>
                {%- else -%}
                  <a href="{{ sib_product.url }}" class="sibling__link{{ modifier_class }}">
                    {{ swatch_image }}
                    <span class="visually-hidden">{{ swatch_color }}</span>
                  </a>
                {%- endif -%}
              </tooltip-component>
            {%- endfor -%}
          </div>
        </div>
      </fieldset>
    {%- endif -%}
  </div>
{%- elsif request.design_mode -%}
  <div {{ block.shopify_attributes }}></div>
{%- endif -%}
